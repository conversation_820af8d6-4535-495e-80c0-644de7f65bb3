import { checkPhoneExists, sendSmsCode, register } from '../../api/auth';

Page({
  data: {
    loading: false,
    username: '',
    phoneNumber: '',
    code: '',
    password: '',
    showPassword: false,
    countdown: 0,
    codeText: '获取验证码',
    timer: null,
    agreeProtocol: true
  },
  
  // 输入用户名
  inputUsername(e) {
    this.setData({
      username: e.detail.value
    });
  },
  
  // 输入手机号
  inputPhoneNumber(e) {
    this.setData({
      phoneNumber: e.detail.value
    });
  },
  
  // 输入验证码
  inputCode(e) {
    this.setData({
      code: e.detail.value
    });
  },
  
  // 输入密码
  inputPassword(e) {
    this.setData({
      password: e.detail.value
    });
  },
  
  // 切换密码显示状态
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },
  
  // 检查手机号是否有效
  isValidPhone() {
    return /^1[3-9]\d{9}$/.test(this.data.phoneNumber);
  },
  
  // 检查是否可以注册
  get canRegister() {
    const { phoneNumber, code, password, username } = this.data;
    return this.isValidPhone() && 
           code.length === 6 && 
           password.length >= 6 && 
           username.length >= 2 &&
           this.data.agreeProtocol;
  },
  
  // 获取验证码
  async getVerificationCode() {
    const { phoneNumber } = this.data;
    
    if (!this.isValidPhone()) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.countdown > 0) {
      return;
    }
    
    this.setData({ loading: true });
    
    try {
      // 检查手机号是否已注册
      const checkResult = await checkPhoneExists(phoneNumber);
      
      if (checkResult.data.exists) {
        wx.showToast({
          title: '该手机号已注册，请直接登录',
          icon: 'none'
        });
        
        setTimeout(() => {
          this.navigateToLogin();
        }, 1500);
        return;
      }
      
      // 发送验证码
      const result = await sendSmsCode(phoneNumber, 'register');
      
      if (result.code === 0) {
        wx.showToast({
          title: '验证码已发送',
          icon: 'success'
        });
        
        // 开始倒计时
        this.setData({
          countdown: 60,
          codeText: '60秒'
        });
        
        this.data.timer = setInterval(() => {
          const countdown = this.data.countdown - 1;
          this.setData({
            countdown,
            codeText: countdown + '秒'
          });
          
          if (countdown === 0) {
            clearInterval(this.data.timer);
            this.setData({
              codeText: '获取验证码'
            });
          }
        }, 1000);
      } else {
        wx.showToast({
          title: result.message || '发送失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('发送验证码失败', error);
      wx.showToast({
        title: '发送失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },
  
  // 注册
  async register() {
    if (!this.canRegister || this.data.loading) return;
    
    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: '请先同意用户协议和隐私政策',
        icon: 'none'
      });
      return;
    }
    
    const { username, phoneNumber, code, password } = this.data;
    
    this.setData({ loading: true });
    wx.showLoading({
      title: '注册中...'
    });
    
    try {
      const result = await register({
        username,
        phone: phoneNumber,
        code,
        password
      });
      
      if (result.code === 0) {
        // 注册成功，保存登录信息
        wx.setStorageSync('token', result.data.token);
        wx.setStorageSync('userInfo', JSON.stringify(result.data.user));
        
        wx.showToast({
          title: '注册成功',
          icon: 'success'
        });
        
        // 引导完善资料
        wx.showModal({
          title: '注册成功',
          content: '欢迎加入驿站帮，是否立即完善个人资料？',
          confirmText: '立即完善',
          cancelText: '以后再说',
          success: (res) => {
            if (res.confirm) {
              // 跳转到个人资料页
              wx.navigateTo({
                url: '/pages/my/profile'
              });
            } else {
              // 返回上一页或首页
              const pages = getCurrentPages();
              if (pages.length > 2) {
                wx.navigateBack({
                  delta: 2
                });
              } else {
                wx.switchTab({
                  url: '/pages/index/index'
                });
              }
            }
          }
        });
      } else {
        wx.showToast({
          title: result.message || '注册失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('注册失败', error);
      wx.showToast({
        title: '注册失败，请检查网络并重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
      this.setData({ loading: false });
    }
  },
  
  // 跳转到登录页面
  navigateToLogin() {
    wx.navigateBack();
  },
  
  // 切换协议同意状态
  toggleAgreement() {
    this.setData({
      agreeProtocol: !this.data.agreeProtocol
    });
  },
  
  // 用户协议
  navigateToAgreement() {
    wx.navigateTo({
      url: '/pages/common/agreement?type=user'
    });
  },
  
  // 隐私政策
  navigateToPrivacy() {
    wx.navigateTo({
      url: '/pages/common/agreement?type=privacy'
    });
  },
  
  onUnload() {
    // 页面销毁时清除定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  }
}); 