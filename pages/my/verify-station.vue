<template>
	<view class="verify-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">驿站认证</text>
			<view class="right-btn" @tap="showHelp">
				<image src="/static/icons/help.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 状态提示 -->
		<view class="status-section" v-if="verificationStatus">
			<view class="status-icon">
				<image :src="statusIcon[verificationStatus]" mode="aspectFit"></image>
			</view>
			<text class="status-text">{{statusText[verificationStatus]}}</text>
			<text class="status-desc">{{statusDesc[verificationStatus]}}</text>
		</view>
		
		<!-- 表单区域 -->
		<view class="form-section" v-if="verificationStatus !== 'approved' && verificationStatus !== 'pending'">
			<view class="form-title">驿站信息认证</view>
			
			<!-- 驿站名称 -->
			<view class="form-item">
				<text class="item-label">驿站名称</text>
				<input 
					class="item-input" 
					type="text" 
					placeholder="请输入完整的驿站名称" 
					v-model="formData.stationName"
					:disabled="isDisabled"
				/>
			</view>
			
			<!-- 驿站编号 -->
			<view class="form-item">
				<text class="item-label">驿站编号</text>
				<input 
					class="item-input" 
					type="text" 
					placeholder="请输入驿站编号" 
					v-model="formData.stationCode"
					:disabled="isDisabled"
				/>
			</view>
			
			<!-- 驿站类型 -->
			<view class="form-item">
				<text class="item-label">驿站类型</text>
				<picker 
					:range="stationTypes" 
					@change="onStationTypeChange" 
					:disabled="isDisabled"
				>
					<view class="picker-box">
						<text class="picker-text">{{formData.stationType || '请选择驿站类型'}}</text>
						<image src="/static/icons/arrow-down.png" mode="aspectFit" class="picker-arrow"></image>
					</view>
				</picker>
			</view>
			
			<!-- 所属品牌 -->
			<view class="form-item">
				<text class="item-label">所属品牌</text>
				<picker 
					:range="brands" 
					@change="onBrandChange" 
					:disabled="isDisabled"
				>
					<view class="picker-box">
						<text class="picker-text">{{formData.brand || '请选择所属品牌'}}</text>
						<image src="/static/icons/arrow-down.png" mode="aspectFit" class="picker-arrow"></image>
					</view>
				</picker>
			</view>
			
			<!-- 详细地址 -->
			<view class="form-item">
				<text class="item-label">详细地址</text>
				<view class="address-input-wrapper">
					<input 
						class="item-input" 
						type="text" 
						placeholder="请输入详细地址" 
						v-model="formData.address"
						:disabled="isDisabled"
					/>
					<view 
						class="location-btn" 
						@tap="useCurrentLocation"
						v-if="!isDisabled"
					>
						<text>定位</text>
					</view>
				</view>
			</view>
			
			<!-- 联系人 -->
			<view class="form-item">
				<text class="item-label">联系人</text>
				<input 
					class="item-input" 
					type="text" 
					placeholder="请输入联系人姓名" 
					v-model="formData.contactName"
					:disabled="isDisabled"
				/>
			</view>
			
			<!-- 联系电话 -->
			<view class="form-item">
				<text class="item-label">联系电话</text>
				<input 
					class="item-input" 
					type="number" 
					placeholder="请输入联系电话" 
					v-model="formData.contactPhone"
					:disabled="isDisabled"
				/>
			</view>
			
			<!-- 营业执照 -->
			<view class="form-item">
				<text class="item-label">营业执照</text>
				<view class="upload-area" @tap="uploadImage('businessLicense')" v-if="!formData.businessLicense || isDisabled">
					<image 
						v-if="formData.businessLicense" 
						:src="formData.businessLicense" 
						mode="aspectFill" 
						class="preview-image"
					></image>
					<view v-else class="upload-placeholder">
						<image src="/static/icons/camera.png" mode="aspectFit"></image>
						<text>点击上传营业执照照片</text>
					</view>
				</view>
				<view v-else class="image-preview">
					<image :src="formData.businessLicense" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap.stop="deleteImage('businessLicense')">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 授权书 -->
			<view class="form-item">
				<text class="item-label">经营授权书 <text class="optional">(选填)</text></text>
				<view class="upload-area" @tap="uploadImage('authorization')" v-if="!formData.authorization || isDisabled">
					<image 
						v-if="formData.authorization" 
						:src="formData.authorization" 
						mode="aspectFill" 
						class="preview-image"
					></image>
					<view v-else class="upload-placeholder">
						<image src="/static/icons/camera.png" mode="aspectFit"></image>
						<text>点击上传授权书照片</text>
					</view>
				</view>
				<view v-else class="image-preview">
					<image :src="formData.authorization" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap.stop="deleteImage('authorization')">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 驿站门头照 -->
			<view class="form-item">
				<text class="item-label">驿站门头照</text>
				<view class="upload-area" @tap="uploadImage('storefront')" v-if="!formData.storefront || isDisabled">
					<image 
						v-if="formData.storefront" 
						:src="formData.storefront" 
						mode="aspectFill" 
						class="preview-image"
					></image>
					<view v-else class="upload-placeholder">
						<image src="/static/icons/camera.png" mode="aspectFit"></image>
						<text>点击上传门头照片</text>
					</view>
				</view>
				<view v-else class="image-preview">
					<image :src="formData.storefront" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap.stop="deleteImage('storefront')">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 驿站内部照 -->
			<view class="form-item">
				<text class="item-label">驿站内部照</text>
				<view class="upload-area" @tap="uploadImage('interior')" v-if="!formData.interior || isDisabled">
					<image 
						v-if="formData.interior" 
						:src="formData.interior" 
						mode="aspectFill" 
						class="preview-image"
					></image>
					<view v-else class="upload-placeholder">
						<image src="/static/icons/camera.png" mode="aspectFit"></image>
						<text>点击上传内部照片</text>
					</view>
				</view>
				<view v-else class="image-preview">
					<image :src="formData.interior" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap.stop="deleteImage('interior')">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 隐私条款 -->
			<view class="agreement-item">
				<view class="checkbox" @tap="toggleAgreement">
					<view class="checkbox-inner" v-if="formData.agreement"></view>
				</view>
				<text class="agreement-text">我已阅读并同意</text>
				<text class="agreement-link" @tap="viewServiceTerms">《驿站认证服务条款》</text>
			</view>
			
			<!-- 提交按钮 -->
			<view 
				class="submit-btn" 
				:class="{'disabled': !isFormValid || isDisabled}"
				@tap="submitVerification"
			>
				<text>{{verificationStatus === 'rejected' ? '重新提交' : '提交认证'}}</text>
			</view>
			
			<!-- 提示文本 -->
			<view class="tips-section">
				<view class="tip-item">
					<text class="dot">•</text>
					<text class="tip-text">请确保所有照片清晰完整，信息真实有效</text>
				</view>
				<view class="tip-item">
					<text class="dot">•</text>
					<text class="tip-text">驿站认证审核通过后，将获得平台更多专属权益</text>
				</view>
				<view class="tip-item">
					<text class="dot">•</text>
					<text class="tip-text">认证审核需要1-3个工作日，请耐心等待</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { 
		submitStationVerification, 
		getStationVerificationStatus, 
		uploadVerificationImage,
		resubmitStationVerification
	} from '../../api/station.js';
	
	export default {
		data() {
			return {
				// 认证状态：null(未申请), pending(审核中), approved(已通过), rejected(已拒绝)
				verificationStatus: null,
				formData: {
					stationName: '',
					stationCode: '',
					stationType: '',
					brand: '',
					address: '',
					longitude: null,
					latitude: null,
					contactName: '',
					contactPhone: '',
					businessLicense: '',
					authorization: '',
					storefront: '',
					interior: '',
					agreement: false
				},
				stationTypes: ['社区驿站', '快递驿站', '校园驿站', '写字楼驿站', '商业驿站'],
				brands: ['顺丰', '京东', '菜鸟', '中通', '圆通', '申通', '韵达', '百世', '其他'],
				statusIcon: {
					pending: '/static/icons/processing.png',
					approved: '/static/icons/success.png',
					rejected: '/static/icons/error.png'
				},
				statusText: {
					pending: '审核中',
					approved: '认证成功',
					rejected: '认证失败'
				},
				statusDesc: {
					pending: '您的驿站认证申请已提交，正在审核中，请耐心等待...',
					approved: '恭喜您，驿站认证已通过！您现在可以使用驿站功能。',
					rejected: '很抱歉，您的驿站认证未通过审核，请检查认证信息或联系客服。'
				}
			}
		},
		computed: {
			isFormValid() {
				const { 
					stationName, stationCode, stationType, brand, address,
					contactName, contactPhone, businessLicense, storefront, interior, agreement
				} = this.formData;
				
				return stationName && stationCode && stationType && brand && address && 
					contactName && contactPhone && businessLicense && storefront && interior && agreement;
			},
			isDisabled() {
				return this.verificationStatus === 'pending' || this.verificationStatus === 'approved';
			}
		},
		onLoad() {
			// 获取认证状态
			this.checkVerificationStatus();
		},
		methods: {
			// 检查认证状态
			async checkVerificationStatus() {
				try {
					uni.showLoading({
						title: '加载中...'
					});
					
					const result = await getStationVerificationStatus();
					
					if (result.code === 0) {
						const { status, verification, remark } = result.data;
						
						this.verificationStatus = status;
						
						// 如果是已拒绝状态，加载上次提交的信息
						if (status === 'rejected' && verification) {
							this.formData = {
								...this.formData,
								stationName: verification.stationName,
								stationCode: verification.stationCode,
								stationType: this.mapStationType(verification.stationType),
								brand: verification.brand,
								address: verification.address,
								longitude: verification.longitude,
								latitude: verification.latitude,
								contactName: verification.contactName,
								contactPhone: verification.contactPhone,
								businessLicense: verification.businessLicense,
								authorization: verification.authorization,
								storefront: verification.storefront,
								interior: verification.interior,
								agreement: true
							};
							
							// 显示拒绝原因
							if (remark) {
								setTimeout(() => {
									uni.showModal({
										title: '认证被拒绝',
										content: `拒绝原因: ${remark}`,
										showCancel: false
									});
								}, 500);
							}
						}
					}
				} catch (error) {
					console.error('获取认证状态失败:', error);
					uni.showToast({
						title: '获取认证状态失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},
			
			// 类型映射
			mapStationType(type) {
				const typeMap = {
					'community': '社区驿站',
					'express': '快递驿站',
					'campus': '校园驿站',
					'office': '写字楼驿站',
					'commercial': '商业驿站'
				};
				return typeMap[type] || type;
			},
			
			// 反向类型映射
			reverseMapStationType(displayType) {
				const reverseTypeMap = {
					'社区驿站': 'community',
					'快递驿站': 'express',
					'校园驿站': 'campus',
					'写字楼驿站': 'office',
					'商业驿站': 'commercial'
				};
				return reverseTypeMap[displayType] || displayType;
			},
			
			// 选择驿站类型
			onStationTypeChange(e) {
				const index = e.detail.value;
				this.formData.stationType = this.stationTypes[index];
			},
			
			// 选择品牌
			onBrandChange(e) {
				const index = e.detail.value;
				this.formData.brand = this.brands[index];
			},
			
			// 使用当前位置
			useCurrentLocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						this.formData.longitude = res.longitude;
						this.formData.latitude = res.latitude;
						
						// 根据经纬度获取地址
						uni.chooseLocation({
							latitude: res.latitude,
							longitude: res.longitude,
							success: (location) => {
								this.formData.address = location.address + location.name;
							}
						});
					},
					fail: () => {
						uni.showToast({
							title: '获取位置失败，请手动输入地址',
							icon: 'none'
						});
					}
				});
			},
			
			// 上传图片
			async uploadImage(type) {
				if (this.isDisabled) return;
				
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: async (res) => {
						const tempFilePath = res.tempFilePaths[0];
						
						uni.showLoading({
							title: '上传中...'
						});
						
						try {
							const result = await uploadVerificationImage(tempFilePath, type);
							
							if (result.code === 0) {
								// 更新对应的图片URL
								this.formData[type] = result.data.url;
								
								uni.showToast({
									title: '上传成功',
									icon: 'success'
								});
							} else {
								uni.showToast({
									title: result.message || '上传失败',
									icon: 'none'
								});
							}
						} catch (error) {
							console.error('上传图片失败:', error);
							uni.showToast({
								title: '上传失败，请重试',
								icon: 'none'
							});
						} finally {
							uni.hideLoading();
						}
					}
				});
			},
			
			// 删除图片
			deleteImage(type) {
				if (this.isDisabled) return;
				
				uni.showModal({
					title: '删除图片',
					content: '确定要删除该图片吗？',
					success: (res) => {
						if (res.confirm) {
							this.formData[type] = '';
						}
					}
				});
			},
			
			// 切换协议同意状态
			toggleAgreement() {
				if (this.isDisabled) return;
				this.formData.agreement = !this.formData.agreement;
			},
			
			// 查看服务条款
			viewServiceTerms() {
				uni.navigateTo({
					url: '/pages/common/agreement?type=station'
				});
			},
			
			// 提交认证
			async submitVerification() {
				if (!this.isFormValid || this.isDisabled) return;
				
				uni.showLoading({
					title: '提交中...'
				});
				
				try {
					// 准备提交数据
					const submitData = {
						...this.formData,
						stationType: this.reverseMapStationType(this.formData.stationType)
					};
					
					// 根据状态决定是提交新申请还是重新提交
					let result;
					if (this.verificationStatus === 'rejected') {
						result = await resubmitStationVerification(submitData);
					} else {
						result = await submitStationVerification(submitData);
					}
					
					if (result.code === 0) {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						});
						
						// 更新状态
						this.verificationStatus = 'pending';
						
						// 延迟返回
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else {
						uni.showToast({
							title: result.message || '提交失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('提交认证申请失败:', error);
					uni.showToast({
						title: '提交失败，请重试',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},
			
			// 显示帮助信息
			showHelp() {
				uni.showModal({
					title: '驿站认证帮助',
					content: '驿站认证是为了确认您的驿站身份，通过认证后可以使用平台提供的驿站功能。如有疑问，请联系客服。',
					showCancel: false
				});
			},
			
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	.verify-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn, .right-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image, .right-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.status-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 50rpx 30rpx;
		background-color: #ffffff;
	}
	
	.status-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
	}
	
	.status-icon image {
		width: 100%;
		height: 100%;
	}
	
	.status-text {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}
	
	.status-desc {
		font-size: 28rpx;
		color: #666666;
		text-align: center;
		line-height: 1.6;
	}
	
	.form-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
	
	.form-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 30rpx;
	}
	
	.form-item {
		margin-bottom: 30rpx;
	}
	
	.item-label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.optional {
		font-size: 24rpx;
		color: #999999;
		font-weight: normal;
	}
	
	.item-input {
		width: 100%;
		height: 90rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333333;
		box-sizing: border-box;
	}
	
	.picker-box {
		width: 100%;
		height: 90rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-sizing: border-box;
	}
	
	.picker-text {
		font-size: 28rpx;
		color: #333333;
	}
	
	.picker-text:empty:before {
		content: '请选择';
		color: #999999;
	}
	
	.picker-arrow {
		width: 30rpx;
		height: 30rpx;
	}
	
	.address-input-wrapper {
		display: flex;
		align-items: center;
	}
	
	.address-input-wrapper .item-input {
		flex: 1;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}
	
	.location-btn {
		width: 120rpx;
		height: 90rpx;
		background-color: #ff5a5f;
		border-top-right-radius: 8rpx;
		border-bottom-right-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.location-btn text {
		font-size: 28rpx;
		color: #ffffff;
	}
	
	.upload-area, .image-preview {
		width: 100%;
		height: 360rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		overflow: hidden;
		position: relative;
	}
	
	.upload-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
	}
	
	.upload-placeholder image {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 20rpx;
	}
	
	.upload-placeholder text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
	}
	
	.delete-btn {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.delete-btn image {
		width: 30rpx;
		height: 30rpx;
	}
	
	.agreement-item {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
	}
	
	.checkbox {
		width: 40rpx;
		height: 40rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.checkbox-inner {
		width: 24rpx;
		height: 24rpx;
		background-color: #ff5a5f;
		border-radius: 4rpx;
	}
	
	.agreement-text {
		font-size: 26rpx;
		color: #666666;
	}
	
	.agreement-link {
		font-size: 26rpx;
		color: #ff5a5f;
	}
	
	.submit-btn {
		width: 100%;
		height: 90rpx;
		background-color: #ff5a5f;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
	}
	
	.submit-btn.disabled {
		background-color: #cccccc;
	}
	
	.submit-btn text {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: bold;
	}
	
	.tips-section {
		margin-top: 30rpx;
	}
	
	.tip-item {
		display: flex;
		margin-bottom: 16rpx;
	}
	
	.dot {
		margin-right: 10rpx;
		color: #999999;
	}
	
	.tip-text {
		font-size: 24rpx;
		color: #999999;
		line-height: 1.6;
	}
</style> 