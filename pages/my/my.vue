<template>
	<view class="my-container">
		<!-- 用户信息区域 -->
		<view class="user-info-section" v-if="isLoggedIn">
			<view class="avatar-container">
				<image class="avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'"></image>
			</view>
			<view class="user-details">
				<text class="username">{{userInfo.nickname || '未设置昵称'}}</text>
				<text class="user-id">ID: {{userInfo._id || '未获取'}}</text>
			</view>
			<view class="edit-profile" @tap="editProfile">
				<text>编辑资料</text>
			</view>
		</view>
		
		<!-- 未登录状态 -->
		<view class="user-info-section not-login" v-else @tap="goToLogin">
			<view class="avatar-container">
				<image class="avatar" src="/static/images/default-avatar.png"></image>
			</view>
			<view class="user-details">
				<text class="username">点击登录</text>
				<text class="user-id">登录后体验更多功能</text>
			</view>
			<view class="login-arrow">
				<text class="arrow">></text>
			</view>
		</view>
		
		<!-- 常用功能区域 -->
		<view class="function-section">
			<view class="section-title">常用功能</view>
			
			<view class="function-grid">
				<view class="function-item" v-for="(item, index) in functionList" :key="item.id" @tap="navigateToFunction(item)">
					<view :class="['function-icon', item.bgColor]">
						<image :src="item.icon" mode="aspectFit"></image>
					</view>
					<text class="function-name">{{item.name}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// 导入验证token所需的API
	import { getLoginStatus } from '@/api/auth';
	import { getToken, setUserInfo, removeToken, removeUserInfo } from '../../utils/auth';
	
	export default {
		data() {
			return {
				isLoggedIn: false,
				userInfo: {},
				// 常用功能列表
				functionList: [
					{
						id: 'posts',
						name: '我的发布',
						icon: '/static/icons/my-posts.png',
						url: '/pages/my/posts',
						bgColor: 'bg-blue',
						requireLogin: true
					},
					{
						id: 'favorites',
						name: '我的收藏',
						icon: '/static/icons/favorites.png',
						url: '/pages/my/favorites',
						bgColor: 'bg-yellow',
						requireLogin: true
					},
					{
						id: 'history',
						name: '浏览历史',
						icon: '/static/icons/history.png',
						url: '/pages/my/history',
						bgColor: 'bg-green',
						requireLogin: false
					},
					{
						id: 'orders',
						name: '我的订单',
						icon: '/static/icons/orders.png',
						url: '/pages/my/orders',
						bgColor: 'bg-purple',
						requireLogin: true
					},
					{
						id: 'wallet',
						name: '我的钱包',
						icon: '/static/icons/wallet.png',
						url: '/pages/my/wallet',
						bgColor: 'bg-pink',
						requireLogin: true
					},
					{
						id: 'cart',
						name: '购物车',
						icon: '/static/icons/cart.png',
						url: '/pages/my/cart',
						bgColor: 'bg-orange',
						requireLogin: true
					},
					{
						id: 'promotion',
						name: '推广赚钱',
						icon: '/static/icons/promotion.png',
						url: '/pages/my/promotion',
						bgColor: 'bg-yellow',
						requireLogin: true
					},
					{
						id: 'verify-identity',
						name: '实名认证',
						icon: '/static/icons/verification.png',
						url: '/pages/my/verify-identity',
						bgColor: 'bg-green',
						requireLogin: true
					},
					{
						id: 'verify-station',
						name: '驿站认证',
						icon: '/static/icons/station-verification.png',
						url: '/pages/my/verify-station',
						bgColor: 'bg-blue',
						requireLogin: true
					},
					{
						id: 'business-settings',
						name: '商务合作',
						icon: '/static/icons/cooperation.png',
						url: '/pages/my/business-settings',
						bgColor: 'bg-purple',
						requireLogin: true
					},
					{
						id: 'service-orders',
						name: '顶班接单',
						icon: '/static/icons/service-orders.png',
						url: '/pages/my/service-orders',
						bgColor: 'bg-yellow',
						requireLogin: true
					},
					{
						id: 'settings',
						name: '设置',
						icon: '/static/icons/settings.png',
						url: '/pages/my/settings',
						bgColor: 'bg-gray',
						requireLogin: false
					}
				]
			}
		},
		onLoad() {
			// 检查登录状态
			this.checkLoginStatus();
		},
		onShow() {
			// 每次显示页面时都检查登录状态，以便更新用户信息
			this.checkLoginStatus();
		},
		methods: {
			// 检查登录状态
			async checkLoginStatus() {
				try {
					// 从本地存储获取用户信息
					const token = uni.getStorageSync('token');
					const userInfoStr = uni.getStorageSync('userInfo');
					
					if (token && userInfoStr) {
						// 解析用户信息 - 确保正确处理JSON字符串或对象
						let userInfo;
						if (typeof userInfoStr === 'string') {
							try {
								userInfo = JSON.parse(userInfoStr);
							} catch (e) {
								console.error('解析用户信息失败:', e);
								userInfo = userInfoStr; // 如果解析失败，可能已经是对象
							}
						} else {
							userInfo = userInfoStr;
						}
						
						this.userInfo = userInfo;
						this.isLoggedIn = true;
						
						// 验证 token 是否有效
						await this.verifyToken();
					} else {
						this.isLoggedIn = false;
						this.userInfo = {};
					}
				} catch (err) {
					console.error('检查登录状态失败:', err);
					this.isLoggedIn = false;
					this.userInfo = {};
				}
			},
			
			// 验证 token 是否有效
			verifyToken() {
				const token = getToken();
				if (!token) {
					this.isLoggedIn = false;
					this.userInfo = null;
					return;
				}

				getLoginStatus()
					.then(res => {
						if (res.code === 0) {
							this.isLoggedIn = true;
							this.userInfo = res.data.user;
							// 更新本地存储的用户信息
							setUserInfo(res.data.user);
						} else {
							this.isLoggedIn = false;
							this.userInfo = null;
							removeToken();
							removeUserInfo();
						}
					})
					.catch(err => {
						console.error('验证Token失败:', err);
						this.isLoggedIn = false;
						this.userInfo = null;
						removeToken();
						removeUserInfo();
					});
			},
			
			// 跳转到登录页面
			goToLogin() {
				uni.navigateTo({
					url: '/pages/my/login'
				});
			},
			
			// 编辑个人资料
			editProfile() {
				uni.navigateTo({
					url: '/pages/my/profile'
				});
			},
			
			// 功能导航
			navigateToFunction(item) {
				// 检查是否需要登录
				if (item.requireLogin && !this.isLoggedIn) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					
					setTimeout(() => {
						this.goToLogin();
					}, 1500);
					return;
				}
				
				// 导航到对应功能页面
				uni.navigateTo({
					url: item.url
				});
			}
		}
	}
</script>

<style>
	.my-container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 40rpx;
	}
	
	.user-info-section {
		background-color: #ffffff;
		padding: 40rpx 30rpx;
		display: flex;
		align-items: center;
	}
	
	.user-info-section.not-login {
		background-color: #ffffff;
	}
	
	.avatar-container {
		position: relative;
	}
	
	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		border: 4rpx solid #f0f0f0;
	}
	
	.user-details {
		flex: 1;
		padding-left: 20rpx;
	}
	
	.username {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
		display: block;
		margin-bottom: 6rpx;
	}
	
	.user-id {
		font-size: 24rpx;
		color: #999999;
	}
	
	.edit-profile {
		padding: 10rpx 20rpx;
		background-color: #f8f8f8;
		border-radius: 30rpx;
	}
	
	.edit-profile text {
		font-size: 24rpx;
		color: #666666;
	}
	
	.login-arrow {
		padding: 10rpx;
	}
	
	.arrow {
		font-size: 30rpx;
		color: #999999;
	}
	
	.function-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 30rpx;
	}
	
	.function-grid {
		display: flex;
		flex-wrap: wrap;
	}
	
	.function-item {
		width: 25%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 40rpx;
	}
	
	.function-icon {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 10rpx;
	}
	
	.function-icon image {
		width: 50rpx;
		height: 50rpx;
	}
	
	.function-name {
		font-size: 26rpx;
		color: #666666;
		text-align: center;
	}
	
	.bg-blue {
		background-color: rgba(0, 122, 255, 0.1);
	}
	
	.bg-yellow {
		background-color: rgba(255, 204, 0, 0.1);
	}
	
	.bg-green {
		background-color: rgba(52, 199, 89, 0.1);
	}
	
	.bg-purple {
		background-color: rgba(175, 82, 222, 0.1);
	}
	
	.bg-pink {
		background-color: rgba(255, 45, 85, 0.1);
	}
	
	.bg-orange {
		background-color: rgba(255, 149, 0, 0.1);
	}
	
	.bg-gray {
		background-color: rgba(142, 142, 147, 0.1);
	}
</style> 