// pages/my/my.js
Page({
  data: {
    userInfo: null,
    isLoggedIn: false,
    functionList: [
      {
        id: 1,
        name: '我的发布',
        icon: '/static/icons/我的发布.png',
        url: '/pages/my/posts'
      },
      {
        id: 2,
        name: '我的收藏',
        icon: '/static/icons/我的收藏.png',
        url: '/pages/my/favorites'
      },
      {
        id: 3,
        name: '驿站认证',
        icon: '/static/icons/shimingrenzheng.png',
        url: '/pages/my/verify-station'
      },
      {
        id: 4,
        name: '实名认证',
        icon: '/static/icons/renzhengyonghu.png',
        url: '/pages/my/verify-identity'
      },
      {
        id: 5,
        name: '我的钱包',
        icon: '/static/icons/我的钱包.png',
        url: '/pages/my/wallet'
      },
      {
        id: 6,
        name: '设置',
        icon: '/static/icons/设置.png',
        url: '/pages/my/settings'
      }
    ]
  },

  onLoad() {
    console.log('我的页面加载')
  },

  onShow() {
    console.log('我的页面显示')
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')
    
    if (token && userInfo) {
      let parsedUserInfo
      try {
        parsedUserInfo = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo
      } catch (e) {
        console.error('解析用户信息失败:', e)
        parsedUserInfo = userInfo
      }
      
      this.setData({
        isLoggedIn: true,
        userInfo: parsedUserInfo
      })
    } else {
      this.setData({
        isLoggedIn: false,
        userInfo: null
      })
    }
  },

  // 跳转到登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/my/login'
    })
  },

  // 编辑个人资料
  editProfile() {
    if (!this.data.isLoggedIn) {
      this.goToLogin()
      return
    }
    
    wx.navigateTo({
      url: '/pages/my/profile'
    })
  },

  // 跳转到功能页面
  navigateToFunction(e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.functionList[index]

    if (!this.data.isLoggedIn && item.id !== 6) { // 设置页面不需要登录
      this.goToLogin()
      return
    }

    wx.navigateTo({
      url: item.url
    })
  },

  // 模拟登录（用于测试）
  mockLogin() {
    const mockUserInfo = {
      id: 1,
      nickname: '测试用户',
      avatar: '/static/images/default-avatar.png',
      phone: '13800138000',
      isStationVerified: true, // 模拟已通过驿站认证
      isStationVerifying: false
    }
    
    wx.setStorageSync('token', 'mock_token_' + Date.now())
    wx.setStorageSync('userInfo', JSON.stringify(mockUserInfo))
    
    this.setData({
      isLoggedIn: true,
      userInfo: mockUserInfo
    })
    
    wx.showToast({
      title: '模拟登录成功',
      icon: 'success'
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('token')
          wx.removeStorageSync('userInfo')
          
          this.setData({
            isLoggedIn: false,
            userInfo: null
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  }
})
