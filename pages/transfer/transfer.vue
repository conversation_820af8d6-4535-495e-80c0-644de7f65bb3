<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-box">
				<image class="search-icon" src="/static/icons/search.png" mode="aspectFit"></image>
				<input type="text" placeholder="搜索驿站位置/类型" v-model="keyword" confirm-type="search" @confirm="search" />
			</view>
			<view class="filter-btn" @tap="toggleFilter">
				<image src="/static/icons/filter.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 快捷筛选 -->
		<view class="quick-tags">
			<scroll-view scroll-x class="tags-scroll-view">
				<view class="tag-item" :class="{'active': activeType === '全部'}" @tap="selectType('全部')">全部</view>
				<view class="tag-item" :class="{'active': activeType === '快递驿站'}" @tap="selectType('快递驿站')">快递驿站</view>
				<view class="tag-item" :class="{'active': activeType === '社区驿站'}" @tap="selectType('社区驿站')">社区驿站</view>
				<view class="tag-item" :class="{'active': activeType === '校园驿站'}" @tap="selectType('校园驿站')">校园驿站</view>
				<view class="tag-item" :class="{'active': activeType === '写字楼驿站'}" @tap="selectType('写字楼驿站')">写字楼驿站</view>
			</scroll-view>
		</view>
		
		<!-- 筛选区域 -->
		<view class="filter-section" v-if="showFilter">
			<view class="filter-row">
				<view class="filter-title">区域</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': selectedArea === '全部'}" @tap="selectArea('全部')">全部</view>
					<view class="filter-option" :class="{'active': selectedArea === '朝阳区'}" @tap="selectArea('朝阳区')">朝阳区</view>
					<view class="filter-option" :class="{'active': selectedArea === '海淀区'}" @tap="selectArea('海淀区')">海淀区</view>
					<view class="filter-option" :class="{'active': selectedArea === '东城区'}" @tap="selectArea('东城区')">东城区</view>
					<view class="filter-option" :class="{'active': selectedArea === '西城区'}" @tap="selectArea('西城区')">西城区</view>
					<view class="filter-option" :class="{'active': selectedArea === '丰台区'}" @tap="selectArea('丰台区')">丰台区</view>
					<view class="filter-option" :class="{'active': selectedArea === '石景山区'}" @tap="selectArea('石景山区')">石景山区</view>
				</view>
			</view>
			<view class="filter-row">
				<view class="filter-title">价格区间</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': selectedPrice === '全部'}" @tap="selectPrice('全部')">全部</view>
					<view class="filter-option" :class="{'active': selectedPrice === '5万以下'}" @tap="selectPrice('5万以下')">5万以下</view>
					<view class="filter-option" :class="{'active': selectedPrice === '5-10万'}" @tap="selectPrice('5-10万')">5-10万</view>
					<view class="filter-option" :class="{'active': selectedPrice === '10-20万'}" @tap="selectPrice('10-20万')">10-20万</view>
					<view class="filter-option" :class="{'active': selectedPrice === '20-50万'}" @tap="selectPrice('20-50万')">20-50万</view>
					<view class="filter-option" :class="{'active': selectedPrice === '50万以上'}" @tap="selectPrice('50万以上')">50万以上</view>
				</view>
			</view>
			<view class="filter-row">
				<view class="filter-title">驿站面积</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': selectedSize === '全部'}" @tap="selectSize('全部')">全部</view>
					<view class="filter-option" :class="{'active': selectedSize === '50m²以下'}" @tap="selectSize('50m²以下')">50m²以下</view>
					<view class="filter-option" :class="{'active': selectedSize === '50-100m²'}" @tap="selectSize('50-100m²')">50-100m²</view>
					<view class="filter-option" :class="{'active': selectedSize === '100-200m²'}" @tap="selectSize('100-200m²')">100-200m²</view>
					<view class="filter-option" :class="{'active': selectedSize === '200m²以上'}" @tap="selectSize('200m²以上')">200m²以上</view>
				</view>
			</view>
			<view class="filter-actions">
				<view class="reset-btn" @tap="resetFilter">重置</view>
				<view class="confirm-btn" @tap="confirmFilter">确认</view>
			</view>
		</view>
		
		<!-- 驿站列表 -->
		<view class="station-list">
			<view class="station-item" v-for="(item, index) in stationList" :key="index" @tap="goToDetail(item.id)">
				<image class="station-image" :src="item.image" mode="aspectFill"></image>
				<view class="station-info">
					<view class="station-title-container">
						<text class="station-title">{{item.title}}</text>
						<view class="verified-badge" v-if="item.isStationVerified">
							<image src="/static/icons/renzhengyonghu.png" class="verified-icon"></image>
							<text class="verified-text">认证</text>
						</view>
					</view>
					<view class="station-tags">
						<text class="station-tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex"
							  :class="{'verified-tag': tag === '驿站认证'}">{{tag}}</text>
					</view>
					<view class="station-address">
						<image src="/static/icons/location.png" mode="aspectFit"></image>
						<text>{{item.address}}</text>
					</view>
					<view class="station-data">
						<view class="data-item">
							<text class="label">面积：</text>
							<text class="value">{{item.space}}m²</text>
						</view>
						<view class="data-item">
							<text class="label">日均件量：</text>
							<text class="value">{{item.package}}件</text>
						</view>
					</view>
					<view class="station-price">¥<text class="price-value">{{item.price}}</text><text class="price-unit">{{item.priceUnit}}</text></view>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view class="loading-more" v-if="loading">
			<text>正在加载更多...</text>
		</view>
		<view class="no-more" v-else-if="!hasMore">
			<text>没有更多数据了</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				keyword: '', // 搜索关键词
				showFilter: false, // 是否显示筛选区域
				activeType: '全部', // 当前选中的类型
				selectedArea: '全部', // 选中的区域
				selectedPrice: '全部', // 选中的价格
				selectedSize: '全部', // 选中的规模
				stationList: [], // 驿站列表
				loading: false, // 是否正在加载
				hasMore: true, // 是否有更多数据
				page: 1, // 当前页码
				pageSize: 10, // 每页数量
				types: ['全部', '快递驿站', '社区驿站', '校园驿站', '写字楼驿站'], // 驿站类型选项
				areas: ['全部', '朝阳区', '海淀区', '东城区', '西城区', '丰台区', '石景山区'], // 区域选项
				prices: ['全部', '5万以下', '5-10万', '10-20万', '20-50万', '50万以上'], // 价格选项
				sizes: ['全部', '50m²以下', '50-100m²', '100-200m²', '200m²以上'], // 规模选项
			}
		},
		computed: {
			// 根据筛选条件过滤驿站列表
			filteredStations() {
				return this.stationList;
			}
		},
		onLoad() {
			// 加载驿站列表数据
			this.loadStations();
		},
		onShow() {
			// 每次页面显示时都重新加载数据，确保从其他页面返回时数据最新
			this.page = 1;
			this.stationList = [];
			this.loadStations();
		},
		onReachBottom() {
			// 触底加载更多
			if (this.hasMore && !this.loading) {
				this.loadMore();
			}
		},
		methods: {
			// 加载驿站列表数据
			async loadStations() {
				this.loading = true;
				
				try {
					// 使用API接口获取驿站列表
					const params = {
						page: this.page,
						pageSize: this.pageSize,
						keyword: this.keyword || '',
						type: this.activeType !== '全部' ? this.activeType : '',
						area: this.selectedArea !== '全部' ? this.selectedArea : '',
						priceRange: this.selectedPrice !== '全部' ? this.getPriceRange(this.selectedPrice) : '',
						sizeRange: this.selectedSize !== '全部' ? this.getSizeRange(this.selectedSize) : ''
					};
					
					const res = await this.$api.station.getStationList(params);
					
					if (res.code === 0) {
						this.stationList = res.data.list || [];
						this.hasMore = this.stationList.length < res.data.total;
					} else {
						uni.showToast({
							title: res.message || '加载数据失败',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('加载驿站数据失败', e);
					uni.showToast({
						title: '网络异常，请稍后重试',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},
			
			// 加载更多数据
			loadMore() {
				this.page++;
				this.loadStations();
			},
			
			// 搜索
			search() {
				this.page = 1;
				this.stationList = [];
				this.loadStations();
			},
			
			// 切换筛选区域显示状态
			toggleFilter() {
				this.showFilter = !this.showFilter;
			},
			
			// 选择类型
			selectType(type) {
				this.activeType = type;
				if (!this.showFilter) {
					this.page = 1;
					this.stationList = [];
					this.loadStations();
				}
			},
			
			// 选择区域
			selectArea(area) {
				this.selectedArea = area;
			},
			
			// 选择价格
			selectPrice(price) {
				this.selectedPrice = price;
			},
			
			// 选择规模
			selectSize(size) {
				this.selectedSize = size;
			},
			
			// 确认筛选
			confirmFilter() {
				this.page = 1;
				this.stationList = [];
				this.loadStations();
				this.showFilter = false;
			},
			
			// 重置筛选条件
			resetFilter() {
				this.activeType = '全部';
				this.selectedArea = '全部';
				this.selectedPrice = '全部';
				this.selectedSize = '全部';
			},
			
			// 获取价格范围参数
			getPriceRange(priceOption) {
				const priceRangeMap = {
					'5万以下': '0,50000',
					'5-10万': '50000,100000',
					'10-20万': '100000,200000',
					'20-50万': '200000,500000',
					'50万以上': '500000,100000000'
				};
				return priceRangeMap[priceOption] || '';
			},
			
			// 获取规模范围参数
			getSizeRange(sizeOption) {
				const sizeRangeMap = {
					'50m²以下': '0,50',
					'50-100m²': '50,100',
					'100-200m²': '100,200',
					'200m²以上': '200,10000'
				};
				return sizeRangeMap[sizeOption] || '';
			},
			
			// 跳转到发布页面
			goToPublish() {
				uni.navigateTo({
					url: '/pages/publish/station'
				});
			},
			
			// 跳转到详情页
			goToDetail(id) {
				// 保存当前点击的驿站完整数据到本地存储
				const currentStation = this.stationList.find(item => item.id.toString() === id.toString());
				if (currentStation) {
					// 将当前点击的站点数据保存到transferList中以确保详情页数据一致
					const transferList = uni.getStorageSync('transferList') || [];
					
					// 查找是否已存在相同ID的数据
					const existingIndex = transferList.findIndex(item => item.id.toString() === id.toString());
					
					if (existingIndex !== -1) {
						// 如果已存在则更新
						transferList[existingIndex] = currentStation;
					} else {
						// 如果不存在则添加
						transferList.push(currentStation);
					}
					
					// 保存到本地存储
					uni.setStorageSync('transferList', transferList);
				}
				
				// 跳转到详情页
				uni.navigateTo({
					url: `/pages/detail/detail?id=${id}&type=station`
				});
			},
			
			// 立即购买
			buyNow(id) {
				uni.navigateTo({
					url: `/pages/my/checkout?id=${id}&type=station`
				});
			}
		}
	}
</script>

<style>
	.container {
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		position: relative;
	}
	
	.search-bar {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		gap: 20rpx;
	}
	
	.search-input-box {
		flex: 1;
		background-color: #ffffff;
		border-radius: 60rpx;
		padding: 20rpx 35rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.search-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}
	
	.filter-btn {
		width: 90rpx;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
		border-radius: 45rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.filter-btn image {
		width: 44rpx;
		height: 44rpx;
	}
	
	/* 快捷分类标签 */
	.quick-tags {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 25rpx 0;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.tags-scroll-view {
		white-space: nowrap;
		padding: 0 25rpx;
	}
	
	.tag-item {
		display: inline-block;
		padding: 12rpx 35rpx;
		margin-right: 20rpx;
		font-size: 28rpx;
		color: #666666;
		background-color: #f5f5f5;
		border-radius: 35rpx;
	}
	
	.tag-item.active {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	/* 筛选区域 */
	.filter-section {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		z-index: 10;
	}
	
	.filter-row {
		margin-bottom: 25rpx;
	}
	
	.filter-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}
	
	.filter-options {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}
	
	.filter-option {
		padding: 12rpx 25rpx;
		background-color: #f5f5f5;
		border-radius: 35rpx;
		font-size: 26rpx;
		color: #666666;
	}
	
	.filter-option.active {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	.filter-actions {
		display: flex;
		justify-content: space-between;
		margin-top: 35rpx;
		gap: 20rpx;
	}
	
	.reset-btn, .confirm-btn {
		flex: 1;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 45rpx;
		font-size: 30rpx;
	}
	
	.reset-btn {
		background-color: #f5f5f5;
		color: #666666;
	}
	
	.confirm-btn {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	/* 驿站列表 */
	.station-list {
		margin-top: 20rpx;
	}
	
	.station-item {
		background-color: #ffffff;
		border-radius: 16rpx;
		margin-bottom: 25rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.station-image {
		width: 100%;
		height: 380rpx;
		background-color: #f9f9f9;
	}
	
	.station-info {
		padding: 25rpx;
	}
	
	.station-title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}
	
	.station-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
		margin-bottom: 20rpx;
	}
	
	.station-tag {
		font-size: 24rpx;
		padding: 8rpx 20rpx;
		background-color: #FFF0F0;
		color: #FF5A5F;
		border-radius: 8rpx;
	}
	
	.station-address {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 20rpx;
	}
	
	.station-address image {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
	}
	
	.station-data {
		display: flex;
		flex-wrap: wrap;
		gap: 30rpx;
		margin-bottom: 20rpx;
	}
	
	.data-item {
		font-size: 26rpx;
		color: #666666;
	}
	
	.data-item .label {
		color: #999999;
	}
	
	.data-item .value {
		color: #333333;
	}
	
	.station-price {
		font-size: 26rpx;
		color: #FF5A5F;
	}
	
	.price-value {
		font-size: 40rpx;
		font-weight: bold;
	}
	
	.price-unit {
		font-size: 26rpx;
		margin-left: 6rpx;
	}
	
	/* 加载更多 */
	.loading-more, .no-more {
		text-align: center;
		padding: 35rpx 0;
	}
	
	.loading-more text, .no-more text {
		font-size: 28rpx;
		color: #999999;
	}
</style> 