// 导入必要的模块
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
// 先尝试加载配置，如果失败则使用默认配置
let config;
try {
  config = require('./config');
} catch (error) {
  console.warn('配置文件加载失败，使用默认配置:', error.message);
  config = {
    server: { port: 3000, host: '0.0.0.0', env: 'development' },
    api: { prefix: '/api' },
    security: {
      cors: { origin: true, credentials: true },
      helmet: { contentSecurityPolicy: false }
    },
    database: { sync: false }
  };
}

// 尝试加载数据库和路由，如果失败则跳过
let sequelize, authRoutes, smsRoutes, statisticsRoutes, stationVerificationRoutes, uploadRoutes, miniprogramRoutes, adminApiRoutes, adminAuthRoutes;
try {
  const db = require('./database/sequelize');
  sequelize = db.sequelize;
  authRoutes = require('./database/routes/authRoutes');
  smsRoutes = require('./database/routes/smsRoutes');
  statisticsRoutes = require('./database/routes/statisticsRoutes');
  stationVerificationRoutes = require('./database/routes/stationVerificationRoutes');
  uploadRoutes = require('./database/routes/uploadRoutes');
  miniprogramRoutes = require('./database/routes/miniprogramRoutes');
  adminApiRoutes = require('./database/routes/adminApiRoutes');
  adminAuthRoutes = require('./database/routes/adminAuthRoutes');
} catch (error) {
  console.warn('数据库或路由模块加载失败，将使用模拟数据:', error.message);
}

// 推广路由（直接在这里定义，避免依赖问题）
const promotionRoutes = require('./database/routes/promotionRoutes');

// 确保环境变量已加载
require('dotenv').config();

// 创建Express应用
const app = express();

// 连接数据库并自动同步模型（可选）
if (sequelize) {
  (async () => {
    try {
      // 验证数据库连接
      await sequelize.authenticate();
      console.log('MySQL数据库连接成功');

      // 如果需要自动同步数据库模型（由配置控制）
      if (config.database && config.database.sync) {
        console.log('正在同步数据库模型...');
        // 导入所有模型确保它们已注册
        try {
          require('./database/models/User');
          require('./database/models/Station');
          require('./database/models/Order');
          require('./database/models/StationVerification');
          // 添加其他模型导入...

          // 使用更安全的同步方式：仅创建不存在的表，不修改已有表结构
          await sequelize.sync({ force: false });
          console.log('数据库模型同步完成');
        } catch (modelError) {
          console.warn('模型同步失败:', modelError.message);
        }
      }
    } catch (error) {
      console.error('数据库连接或同步失败:', error);
      // 只记录错误，不要立即退出进程，让应用继续运行
      console.error('服务器将继续运行，但某些数据库功能可能不可用');
    }
  })().catch(err => {
    console.error('数据库初始化异步错误:', err);
  });
} else {
  console.log('数据库模块未加载，服务器将以无数据库模式运行');
}

// 安全中间件
app.use(helmet(config.security.helmet));

// 跨域配置
app.use(cors(config.security.cors));

// 请求限流
const limiter = rateLimit(config.api.rateLimit);
app.use(limiter);

// 请求解析中间件
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'dist')));
// 上传文件静态服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// API路由（条件性注册）
if (authRoutes) app.use(`${config.api.prefix}/auth`, authRoutes);
if (smsRoutes) app.use(`${config.api.prefix}/sms`, smsRoutes);
if (statisticsRoutes) app.use(`${config.api.prefix}/statistics`, statisticsRoutes);
if (stationVerificationRoutes) app.use(`${config.api.prefix}/station/verify`, stationVerificationRoutes);
if (uploadRoutes) app.use(`${config.api.prefix}/upload`, uploadRoutes);
if (miniprogramRoutes) app.use(`${config.api.prefix}/miniprogram`, miniprogramRoutes);
if (adminApiRoutes) app.use(`${config.api.prefix}/admin`, adminApiRoutes);
if (adminAuthRoutes) app.use(`${config.api.prefix}/admin/auth`, adminAuthRoutes);

// 推广路由（重要：确保这个路由始终可用）
try {
  app.use(`${config.api.prefix}/admin/promotion`, promotionRoutes);
  console.log('推广系统API路由已注册');
} catch (error) {
  console.error('推广路由注册失败:', error);
  // 如果推广路由失败，创建一个简单的备用路由
  app.get(`${config.api.prefix}/admin/promotion/commission-ranking`, (req, res) => {
    res.json({
      code: 0,
      message: '获取佣金排行成功（备用数据）',
      data: [
        { id: 1, nickname: '推广达人001', level: 3, total_commission: 15678.90 },
        { id: 2, nickname: '超级推广员', level: 4, total_commission: 12345.67 }
      ]
    });
  });

  app.get(`${config.api.prefix}/admin/promotion/invites-ranking`, (req, res) => {
    res.json({
      code: 0,
      message: '获取推广用户排行成功（备用数据）',
      data: [
        { id: 1, nickname: '推广王者', level: 4, invite_count: 156 },
        { id: 2, nickname: '邀请达人', level: 3, invite_count: 134 }
      ]
    });
  });
}

// 测试路由
app.get('/api/test', (req, res) => {
  res.json({
    code: 0,
    message: '服务器运行正常',
    time: new Date().toISOString(),
    env: process.env.NODE_ENV,
    database: 'MySQL'
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 所有其他请求返回index.html - 用于前端路由
// 注意：确保这个路由放在最后，避免拦截API请求
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// 启动服务器
const server = app.listen(config.server.port, config.server.host, () => {
  console.log(`服务器运行在端口: ${config.server.port}`);
  console.log(`环境: ${config.server.env}`);
  console.log(`后台管理系统: http://localhost:${config.server.port}`);
  console.log('API接口已启用，小程序与后台管理系统可以进行数据互通');
  console.log(`测试API: http://localhost:${config.server.port}${config.api.prefix}/test`);
});

// 优雅关闭服务器
process.on('SIGTERM', async () => {
  console.log('SIGTERM信号接收到，关闭服务器...');
  server.close(async () => {
    console.log('HTTP服务器已关闭');
    // 关闭数据库连接
    try {
      await sequelize.close();
      console.log('数据库连接已关闭');
      process.exit(0);
    } catch (error) {
      console.error('关闭数据库连接时出错:', error);
      process.exit(1);
    }
  });
}); 