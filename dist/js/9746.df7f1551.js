"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[9746],{9746:(e,o,a)=>{a.r(o),a.d(o,{default:()=>y});var s=a(6768),n=a(4232),l=a(5130);const r={class:"login-container"},t={class:"title-container"},d={class:"title"},i={class:"svg-container"},u={class:"svg-container"},m={style:{"margin-bottom":"20px"}};function p(e,o,a,p,c,g){const v=(0,s.g2)("el-button"),w=(0,s.g2)("svg-icon"),h=(0,s.g2)("el-input"),k=(0,s.g2)("el-form-item"),b=(0,s.g2)("el-checkbox"),y=(0,s.g2)("el-form");return(0,s.uX)(),(0,s.CE)("div",r,[(0,s.bF)(y,{ref:"loginForm",model:p.loginForm,rules:p.loginRules,class:"login-form",autocomplete:"on","label-position":"left"},{default:(0,s.k6)((()=>[(0,s.Lk)("div",t,[(0,s.Lk)("h3",d,(0,n.v_)(p.isAdminLogin?"管理员登录":"用户登录"),1),(0,s.bF)(v,{size:"mini",type:"text",onClick:p.switchLoginType},{default:(0,s.k6)((()=>[(0,s.eW)((0,n.v_)(p.isAdminLogin?"切换到用户登录":"切换到管理员登录"),1)])),_:1},8,["onClick"])]),(0,s.bF)(k,{prop:"username"},{default:(0,s.k6)((()=>[(0,s.Lk)("span",i,[(0,s.bF)(w,{"icon-class":"user"})]),(0,s.bF)(h,{ref:"username",modelValue:p.loginForm.username,"onUpdate:modelValue":o[0]||(o[0]=e=>p.loginForm.username=e),placeholder:p.isAdminLogin?"管理员账号":"手机号",name:"username",type:"text",tabindex:"1",autocomplete:"on"},null,8,["modelValue","placeholder"])])),_:1}),(0,s.bF)(k,{prop:"password"},{default:(0,s.k6)((()=>[(0,s.Lk)("span",u,[(0,s.bF)(w,{"icon-class":"password"})]),((0,s.uX)(),(0,s.Wv)(h,{key:p.passwordType,ref:"password",modelValue:p.loginForm.password,"onUpdate:modelValue":o[1]||(o[1]=e=>p.loginForm.password=e),type:p.passwordType,placeholder:"密码",name:"password",tabindex:"2",autocomplete:"on",onKeyup:(0,l.jR)(p.handleLogin,["enter","native"])},null,8,["modelValue","type","onKeyup"])),(0,s.Lk)("span",{class:"show-pwd",onClick:o[2]||(o[2]=(...e)=>p.showPwd&&p.showPwd(...e))},[(0,s.bF)(w,{"icon-class":"password"===p.passwordType?"eye":"eye-open"},null,8,["icon-class"])])])),_:1}),(0,s.Lk)("div",m,[(0,s.bF)(b,{modelValue:p.loginForm.rememberMe,"onUpdate:modelValue":o[3]||(o[3]=e=>p.loginForm.rememberMe=e)},{default:(0,s.k6)((()=>o[4]||(o[4]=[(0,s.eW)("记住我")]))),_:1},8,["modelValue"])]),(0,s.bF)(v,{loading:p.loading,type:"primary",style:{width:"100%","margin-bottom":"30px"},onClick:(0,l.D$)(p.handleLogin,["prevent"])},{default:(0,s.k6)((()=>o[5]||(o[5]=[(0,s.eW)(" 登录 ")]))),_:1},8,["loading","onClick"])])),_:1},8,["model","rules"])])}var c=a(782),g=a(144),v=a(1387),w=a(1219);a(3603);const h={name:"Login",setup(){const e=(0,c.Pj)(),o=(0,v.rd)(),a=(0,g.Kh)({username:"",password:"",rememberMe:!1}),n=(0,g.KR)(!1),l=(0,g.KR)("password"),r=(0,g.KR)(!1),t=(0,g.KR)(void 0),d=(0,g.KR)({}),i={username:[{required:!0,trigger:"blur",validator:u}],password:[{required:!0,trigger:"blur",validator:m}]},u=(e,o,a)=>{o?a():a(new Error(r.value?"请输入管理员账号":"请输入手机号"))},m=(e,o,a)=>{o.length<6?a(new Error("密码不能少于6位")):a()},p=()=>{"password"===l.value?l.value="":l.value="password",nextTick((()=>{refs.password.focus()}))},h=()=>{r.value=!r.value,a.username="",a.password="";const e={...d.value};r.value?e.admin="true":delete e.admin,o.replace({path:"/login",query:e})},k=()=>{refs.loginForm.validate((s=>{if(!s)return console.log("表单验证失败"),!1;{n.value=!0;const s=r.value?"admin/login":"user/login",l=r.value?{username:a.username,password:a.password}:{phone:a.username,password:a.password};e.dispatch(s,l).then((()=>{w.nk.success("登录成功");const a=r.value?t.value||"/admin/dashboard":t.value||"/";o.push({path:a,query:d.value}),setTimeout((()=>{console.log("登录成功后的token:",r.value?e.getters.adminToken:e.getters.token),r.value&&location.reload()}),1e3),n.value=!1})).catch((e=>{console.error("登录失败:",e),w.nk.error(e.message||"登录失败，请检查用户名和密码"),n.value=!1}))}}))};(0,s.wB)((()=>o.currentRoute),{handler(e){const o=e.query;o&&(t.value=o.redirect,d.value=b(o),"true"===o.admin&&(r.value=!0))},immediate:!0}),(0,s.sV)((()=>{e.getters.token&&!r.value&&o.push({path:t.value||"/"}),e.getters.adminToken&&r.value&&o.push({path:t.value||"/admin/dashboard"})}));const b=e=>Object.keys(e).reduce(((o,a)=>("redirect"!==a&&(o[a]=e[a]),o)),{});return{loginForm:a,loading:n,passwordType:l,isAdminLogin:r,redirect:t,otherQuery:d,loginRules:i,showPwd:p,switchLoginType:h,handleLogin:k,getOtherQuery:b}}};var k=a(1241);const b=(0,k.A)(h,[["render",p],["__scopeId","data-v-d3d5c7d4"]]),y=b}}]);