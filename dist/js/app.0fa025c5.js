(()=>{var e={1579:(e,t,n)=>{"use strict";n.d(t,{b:()=>v,A:()=>_});var i=n(1387),o=n(6768),a=n(4232),s=n(5130);const r={class:"app-wrapper"},l={class:"logo-container"},d={class:"title"},c={class:"main-container"},m={class:"navbar"},u={class:"right-menu"},p={class:"app-main"};function h(e,t,n,i,h,f){const b=(0,o.g2)("el-menu-item"),k=(0,o.g2)("el-sub-menu"),g=(0,o.g2)("el-menu"),v=(0,o.g2)("el-dropdown-item"),S=(0,o.g2)("el-dropdown-menu"),_=(0,o.g2)("el-dropdown"),T=(0,o.g2)("router-view");return(0,o.uX)(),(0,o.CE)("div",r,[(0,o.Lk)("div",{class:(0,a.C4)(["sidebar-container",{"is-collapsed":h.isCollapse}])},[(0,o.Lk)("div",l,[t[1]||(t[1]=(0,o.Lk)("div",{class:"logo"},[(0,o.Lk)("i",{class:"el-icon-s-platform"})],-1)),(0,o.bo)((0,o.Lk)("h1",d,"驿站帮Pro",512),[[s.aG,!h.isCollapse]])]),(0,o.bF)(g,{"default-active":f.activeMenu,"background-color":"transparent","text-color":"#bfcbd9","active-text-color":"#ffffff",collapse:h.isCollapse,"unique-opened":!0,router:"",mode:"vertical",class:"sidebar-menu"},{default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/dashboard"},{default:(0,o.k6)((()=>t[2]||(t[2]=[(0,o.Lk)("i",{class:"el-icon-data-analysis"},null,-1),(0,o.Lk)("span",null,"仪表板",-1)]))),_:1}),(0,o.bF)(k,{index:"system"},{title:(0,o.k6)((()=>t[3]||(t[3]=[(0,o.Lk)("i",{class:"el-icon-setting"},null,-1),(0,o.Lk)("span",null,"系统管理",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/system/user"},{default:(0,o.k6)((()=>t[4]||(t[4]=[(0,o.eW)("用户管理")]))),_:1}),(0,o.bF)(b,{index:"/system/role"},{default:(0,o.k6)((()=>t[5]||(t[5]=[(0,o.eW)("角色管理")]))),_:1}),(0,o.bF)(b,{index:"/system/menu"},{default:(0,o.k6)((()=>t[6]||(t[6]=[(0,o.eW)("菜单管理")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"station"},{title:(0,o.k6)((()=>t[7]||(t[7]=[(0,o.Lk)("i",{class:"el-icon-s-shop"},null,-1),(0,o.Lk)("span",null,"驿站管理",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/station/list"},{default:(0,o.k6)((()=>t[8]||(t[8]=[(0,o.eW)("驿站列表")]))),_:1}),(0,o.bF)(b,{index:"/station/verify"},{default:(0,o.k6)((()=>t[9]||(t[9]=[(0,o.eW)("认证审核")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"order"},{title:(0,o.k6)((()=>t[10]||(t[10]=[(0,o.Lk)("i",{class:"el-icon-s-order"},null,-1),(0,o.Lk)("span",null,"订单管理",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/order/list"},{default:(0,o.k6)((()=>t[11]||(t[11]=[(0,o.eW)("订单列表")]))),_:1}),(0,o.bF)(b,{index:"/order/refund"},{default:(0,o.k6)((()=>t[12]||(t[12]=[(0,o.eW)("退款管理")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"service"},{title:(0,o.k6)((()=>t[13]||(t[13]=[(0,o.Lk)("i",{class:"el-icon-s-tools"},null,-1),(0,o.Lk)("span",null,"服务管理",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/service/list"},{default:(0,o.k6)((()=>t[14]||(t[14]=[(0,o.eW)("服务列表")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"job"},{title:(0,o.k6)((()=>t[15]||(t[15]=[(0,o.Lk)("i",{class:"el-icon-suitcase"},null,-1),(0,o.Lk)("span",null,"工作岗位",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/job/list"},{default:(0,o.k6)((()=>t[16]||(t[16]=[(0,o.eW)("岗位列表")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"device"},{title:(0,o.k6)((()=>t[17]||(t[17]=[(0,o.Lk)("i",{class:"el-icon-box"},null,-1),(0,o.Lk)("span",null,"设备管理",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/device/list"},{default:(0,o.k6)((()=>t[18]||(t[18]=[(0,o.eW)("设备列表")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"content"},{title:(0,o.k6)((()=>t[19]||(t[19]=[(0,o.Lk)("i",{class:"el-icon-document"},null,-1),(0,o.Lk)("span",null,"内容管理",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/content/audit"},{default:(0,o.k6)((()=>t[20]||(t[20]=[(0,o.eW)("统一审核中心")]))),_:1}),(0,o.bF)(b,{index:"/content/management"},{default:(0,o.k6)((()=>t[21]||(t[21]=[(0,o.eW)("内容上下架管理")]))),_:1}),(0,o.bF)(b,{index:"/content/quality"},{default:(0,o.k6)((()=>t[22]||(t[22]=[(0,o.eW)("内容质量管理")]))),_:1}),(0,o.bF)(b,{index:"/content/banner"},{default:(0,o.k6)((()=>t[23]||(t[23]=[(0,o.eW)("轮播图管理")]))),_:1}),(0,o.bF)(b,{index:"/content/notice"},{default:(0,o.k6)((()=>t[24]||(t[24]=[(0,o.eW)("公告管理")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"statistics"},{title:(0,o.k6)((()=>t[25]||(t[25]=[(0,o.Lk)("i",{class:"el-icon-s-data"},null,-1),(0,o.Lk)("span",null,"统计分析",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/statistics/overview"},{default:(0,o.k6)((()=>t[26]||(t[26]=[(0,o.eW)("业务概览")]))),_:1}),(0,o.bF)(b,{index:"/statistics/user"},{default:(0,o.k6)((()=>t[27]||(t[27]=[(0,o.eW)("用户分析")]))),_:1}),(0,o.bF)(b,{index:"/statistics/order"},{default:(0,o.k6)((()=>t[28]||(t[28]=[(0,o.eW)("订单分析")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"transfer"},{title:(0,o.k6)((()=>t[29]||(t[29]=[(0,o.Lk)("i",{class:"el-icon-shop"},null,-1),(0,o.Lk)("span",null,"驿站转让",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/transfer/list"},{default:(0,o.k6)((()=>t[30]||(t[30]=[(0,o.eW)("转让列表")]))),_:1}),(0,o.bF)(b,{index:"/transfer/statistics"},{default:(0,o.k6)((()=>t[31]||(t[31]=[(0,o.eW)("转让统计")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"equipment"},{title:(0,o.k6)((()=>t[32]||(t[32]=[(0,o.Lk)("i",{class:"el-icon-printer"},null,-1),(0,o.Lk)("span",null,"设备交易",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/equipment/list"},{default:(0,o.k6)((()=>t[33]||(t[33]=[(0,o.eW)("设备列表")]))),_:1}),(0,o.bF)(b,{index:"/equipment/category"},{default:(0,o.k6)((()=>t[34]||(t[34]=[(0,o.eW)("设备分类")]))),_:1}),(0,o.bF)(b,{index:"/equipment/price"},{default:(0,o.k6)((()=>t[35]||(t[35]=[(0,o.eW)("价格参考")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"recruitment"},{title:(0,o.k6)((()=>t[36]||(t[36]=[(0,o.Lk)("i",{class:"el-icon-briefcase"},null,-1),(0,o.Lk)("span",null,"招聘求职",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/recruitment/list"},{default:(0,o.k6)((()=>t[37]||(t[37]=[(0,o.eW)("招聘列表")]))),_:1}),(0,o.bF)(b,{index:"/recruitment/category"},{default:(0,o.k6)((()=>t[38]||(t[38]=[(0,o.eW)("职位分类")]))),_:1}),(0,o.bF)(b,{index:"/recruitment/statistics"},{default:(0,o.k6)((()=>t[39]||(t[39]=[(0,o.eW)("招聘统计")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"substitution"},{title:(0,o.k6)((()=>t[40]||(t[40]=[(0,o.Lk)("i",{class:"el-icon-timer"},null,-1),(0,o.Lk)("span",null,"顶班服务",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/substitution/list"},{default:(0,o.k6)((()=>t[41]||(t[41]=[(0,o.eW)("顶班列表")]))),_:1}),(0,o.bF)(b,{index:"/substitution/application"},{default:(0,o.k6)((()=>t[42]||(t[42]=[(0,o.eW)("接单申请")]))),_:1}),(0,o.bF)(b,{index:"/substitution/evaluation"},{default:(0,o.k6)((()=>t[43]||(t[43]=[(0,o.eW)("服务评价")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"shop"},{title:(0,o.k6)((()=>t[44]||(t[44]=[(0,o.Lk)("i",{class:"el-icon-shopping-bag"},null,-1),(0,o.Lk)("span",null,"商城管理",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/shop/decoration"},{default:(0,o.k6)((()=>t[45]||(t[45]=[(0,o.eW)("小程序装修")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"promotion"},{title:(0,o.k6)((()=>t[46]||(t[46]=[(0,o.Lk)("i",{class:"el-icon-present"},null,-1),(0,o.Lk)("span",null,"推广赚钱",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/promotion/user"},{default:(0,o.k6)((()=>t[47]||(t[47]=[(0,o.eW)("推广员管理")]))),_:1}),(0,o.bF)(b,{index:"/promotion/code"},{default:(0,o.k6)((()=>t[48]||(t[48]=[(0,o.eW)("推广码管理")]))),_:1}),(0,o.bF)(b,{index:"/promotion/commission"},{default:(0,o.k6)((()=>t[49]||(t[49]=[(0,o.eW)("佣金规则")]))),_:1}),(0,o.bF)(b,{index:"/promotion/withdrawal"},{default:(0,o.k6)((()=>t[50]||(t[50]=[(0,o.eW)("提现管理")]))),_:1}),(0,o.bF)(b,{index:"/promotion/statistics"},{default:(0,o.k6)((()=>t[51]||(t[51]=[(0,o.eW)("推广统计")]))),_:1})])),_:1}),(0,o.bF)(k,{index:"identity"},{title:(0,o.k6)((()=>t[52]||(t[52]=[(0,o.Lk)("i",{class:"el-icon-user-filled"},null,-1),(0,o.Lk)("span",null,"实名认证管理",-1)]))),default:(0,o.k6)((()=>[(0,o.bF)(b,{index:"/identity/list"},{default:(0,o.k6)((()=>t[53]||(t[53]=[(0,o.eW)("认证列表")]))),_:1}),(0,o.bF)(b,{index:"/identity/statistics"},{default:(0,o.k6)((()=>t[54]||(t[54]=[(0,o.eW)("认证统计")]))),_:1})])),_:1})])),_:1},8,["default-active","collapse"])],2),(0,o.Lk)("div",c,[(0,o.Lk)("div",m,[(0,o.Lk)("div",{class:"hamburger-container",onClick:t[0]||(t[0]=(...e)=>f.toggleSideBar&&f.toggleSideBar(...e))},[(0,o.Lk)("i",{class:(0,a.C4)(h.isCollapse?"el-icon-s-unfold":"el-icon-s-fold")},null,2)]),(0,o.Lk)("div",u,[(0,o.bF)(_,{trigger:"click"},{dropdown:(0,o.k6)((()=>[(0,o.bF)(S,null,{default:(0,o.k6)((()=>[(0,o.bF)(v,null,{default:(0,o.k6)((()=>t[55]||(t[55]=[(0,o.eW)("个人中心")]))),_:1}),(0,o.bF)(v,{divided:""},{default:(0,o.k6)((()=>t[56]||(t[56]=[(0,o.eW)("退出登录")]))),_:1})])),_:1})])),default:(0,o.k6)((()=>[t[57]||(t[57]=(0,o.Lk)("span",{class:"el-dropdown-link"},[(0,o.eW)(" 管理员 "),(0,o.Lk)("i",{class:"el-icon-arrow-down"})],-1))])),_:1})])]),(0,o.Lk)("div",p,[(0,o.bF)(T)])])])}const f={name:"Layout",data(){return{isCollapse:!1}},computed:{activeMenu(){const e=this.$route,{meta:t,path:n}=e;return t.activeMenu?t.activeMenu:n}},methods:{toggleSideBar(){this.isCollapse=!this.isCollapse}}};var b=n(1241);const k=(0,b.A)(f,[["render",h],["__scopeId","data-v-3cdc8bdd"]]),g=k,v=[{path:"/login",component:()=>n.e(9746).then(n.bind(n,9746)),hidden:!0},{path:"/404",component:()=>n.e(6960).then(n.bind(n,6960)),hidden:!0},{path:"/",component:g,redirect:"/dashboard",children:[{path:"dashboard",component:()=>Promise.all([n.e(2032),n.e(2977)]).then(n.bind(n,2977)),name:"Dashboard",meta:{title:"仪表盘",icon:"Odometer",affix:!0}}]},{path:"/system",component:g,redirect:"/system/user",name:"System",meta:{title:"系统管理",icon:"Setting"},children:[{path:"user",component:()=>n.e(1196).then(n.bind(n,1196)),name:"User",meta:{title:"用户管理",icon:"User"}},{path:"role",component:()=>n.e(8591).then(n.bind(n,8591)),name:"Role",meta:{title:"角色管理",icon:"UserFilled"}},{path:"menu",component:()=>n.e(9629).then(n.bind(n,9629)),name:"Menu",meta:{title:"菜单管理",icon:"Menu"}}]},{path:"/station",component:g,redirect:"/station/list",name:"Station",meta:{title:"驿站管理",icon:"House"},children:[{path:"list",component:()=>n.e(5285).then(n.bind(n,5285)),name:"StationList",meta:{title:"驿站列表",icon:"List"}},{path:"verify",component:()=>n.e(6675).then(n.bind(n,6675)),name:"StationVerify",meta:{title:"认证审核",icon:"Checked"}}]},{path:"/order",component:g,redirect:"/order/list",name:"Order",meta:{title:"订单管理",icon:"Tickets"},children:[{path:"list",component:()=>n.e(2045).then(n.bind(n,2045)),name:"OrderList",meta:{title:"订单列表",icon:"List"}},{path:"refund",component:()=>n.e(1674).then(n.bind(n,1674)),name:"OrderRefund",meta:{title:"退款管理",icon:"TurnOff"}}]},{path:"/service",component:g,redirect:"/service/list",name:"Service",meta:{title:"服务管理",icon:"Service"},children:[{path:"list",component:()=>n.e(6074).then(n.bind(n,6074)),name:"ServiceList",meta:{title:"服务列表",icon:"List"}}]},{path:"/job",component:g,redirect:"/job/list",name:"Job",meta:{title:"工作岗位",icon:"Suitcase"},children:[{path:"list",component:()=>n.e(2917).then(n.bind(n,2917)),name:"JobList",meta:{title:"岗位列表",icon:"List"}}]},{path:"/device",component:g,redirect:"/device/list",name:"Device",meta:{title:"设备管理",icon:"Box"},children:[{path:"list",component:()=>n.e(1669).then(n.bind(n,1669)),name:"DeviceList",meta:{title:"设备列表",icon:"List"}}]},{path:"/content",component:g,redirect:"/content/banner",name:"Content",meta:{title:"内容管理",icon:"Document"},children:[{path:"audit",component:()=>n.e(5882).then(n.bind(n,5882)),name:"ContentAudit",meta:{title:"统一审核中心",icon:"Check"}},{path:"audit/detail/:id/:type",component:()=>n.e(5382).then(n.bind(n,5382)),name:"AuditDetail",meta:{title:"审核详情",icon:"View",activeMenu:"/content/audit"},hidden:!0},{path:"management",component:()=>Promise.all([n.e(2032),n.e(8504)]).then(n.bind(n,8504)),name:"ContentManagement",meta:{title:"内容上下架管理",icon:"SwitchButton"}},{path:"quality",component:()=>n.e(5329).then(n.bind(n,5329)),name:"ContentQuality",meta:{title:"内容质量管理",icon:"Star"}},{path:"banner",component:()=>n.e(2520).then(n.bind(n,2520)),name:"Banner",meta:{title:"轮播图管理",icon:"Picture"}},{path:"notice",component:()=>n.e(3758).then(n.bind(n,3758)),name:"Notice",meta:{title:"公告管理",icon:"Bell"}}]},{path:"/statistics",component:g,redirect:"/statistics/overview",name:"Statistics",meta:{title:"统计分析",icon:"DataAnalysis"},children:[{path:"overview",component:()=>n.e(1871).then(n.bind(n,1871)),name:"Overview",meta:{title:"业务概览",icon:"DataLine"}},{path:"user",component:()=>n.e(7232).then(n.bind(n,7232)),name:"UserStatistics",meta:{title:"用户分析",icon:"User"}},{path:"order",component:()=>n.e(5903).then(n.bind(n,5903)),name:"OrderStatistics",meta:{title:"订单分析",icon:"Tickets"}}]},{path:"/profile",component:g,hidden:!0,children:[{path:"",component:()=>n.e(865).then(n.bind(n,865)),name:"Profile",meta:{title:"个人中心",icon:"User"}}]},{path:"/transfer",component:g,redirect:"/transfer/list",name:"Transfer",meta:{title:"驿站转让",icon:"Shop"},children:[{path:"list",component:()=>n.e(5772).then(n.bind(n,5772)),name:"TransferList",meta:{title:"转让列表",icon:"List"}},{path:"detail/:id",component:()=>n.e(9374).then(n.bind(n,9374)),name:"TransferDetail",meta:{title:"转让详情",icon:"View",activeMenu:"/transfer/list"},hidden:!0},{path:"statistics",component:()=>Promise.all([n.e(2032),n.e(8258)]).then(n.bind(n,8258)),name:"TransferStatistics",meta:{title:"转让统计",icon:"PieChart"}}]},{path:"/equipment",component:g,redirect:"/equipment/list",name:"Equipment",meta:{title:"设备交易",icon:"Printer"},children:[{path:"list",component:()=>n.e(5757).then(n.bind(n,5757)),name:"EquipmentList",meta:{title:"设备列表",icon:"List"}},{path:"detail/:id",component:()=>n.e(4685).then(n.bind(n,4685)),name:"EquipmentDetail",meta:{title:"设备详情",icon:"View",activeMenu:"/equipment/list"},hidden:!0},{path:"category",component:()=>n.e(8111).then(n.bind(n,492)),name:"EquipmentCategory",meta:{title:"设备分类",icon:"Folder"}},{path:"price",component:()=>Promise.all([n.e(2032),n.e(8709)]).then(n.bind(n,8709)),name:"EquipmentPrice",meta:{title:"价格参考",icon:"Money"}}]},{path:"/recruitment",component:g,redirect:"/recruitment/list",name:"Recruitment",meta:{title:"招聘求职",icon:"Briefcase"},children:[{path:"list",component:()=>n.e(8945).then(n.bind(n,8945)),name:"RecruitmentList",meta:{title:"招聘列表",icon:"List"}},{path:"detail/:id",component:()=>n.e(6955).then(n.bind(n,6955)),name:"RecruitmentDetail",meta:{title:"招聘详情",icon:"View",activeMenu:"/recruitment/list"},hidden:!0},{path:"category",component:()=>n.e(2508).then(n.bind(n,2508)),name:"JobCategory",meta:{title:"职位分类",icon:"Folder"}},{path:"statistics",component:()=>n.e(9641).then(n.bind(n,9641)),name:"RecruitmentStatistics",meta:{title:"招聘统计",icon:"PieChart"}}]},{path:"/substitution",component:g,redirect:"/substitution/list",name:"Substitution",meta:{title:"顶班服务",icon:"Timer"},children:[{path:"list",component:()=>n.e(9273).then(n.bind(n,9273)),name:"SubstitutionList",meta:{title:"顶班列表",icon:"List"}},{path:"detail/:id",component:()=>n.e(6653).then(n.bind(n,6653)),name:"SubstitutionDetail",meta:{title:"顶班详情",icon:"View",activeMenu:"/substitution/list"},hidden:!0},{path:"application",component:()=>n.e(9298).then(n.bind(n,9298)),name:"SubstitutionApplication",meta:{title:"接单申请",icon:"Finished"}},{path:"evaluation",component:()=>n.e(6610).then(n.bind(n,6610)),name:"SubstitutionEvaluation",meta:{title:"服务评价",icon:"Star"}}]},{path:"/shop",component:g,redirect:"/shop/decoration",name:"Shop",meta:{title:"商城管理",icon:"ShoppingBag"},children:[{path:"decoration",component:()=>n.e(7550).then(n.bind(n,7550)),name:"ShopDecoration",meta:{title:"小程序装修",icon:"Brush"}}]},{path:"/promotion",component:g,redirect:"/promotion/user",name:"Promotion",meta:{title:"推广赚钱",icon:"Present"},children:[{path:"user",component:()=>n.e(9954).then(n.bind(n,9954)),name:"PromoterUser",meta:{title:"推广员管理",icon:"User"}},{path:"code",component:()=>n.e(9414).then(n.bind(n,9414)),name:"PromoCode",meta:{title:"推广码管理",icon:"DocumentCopy"}},{path:"commission",component:()=>n.e(1327).then(n.bind(n,1327)),name:"Commission",meta:{title:"佣金规则",icon:"SetUp"}},{path:"withdrawal",component:()=>n.e(4076).then(n.bind(n,4076)),name:"Withdrawal",meta:{title:"提现管理",icon:"Money"}},{path:"statistics",component:()=>Promise.all([n.e(2032),n.e(7722)]).then(n.bind(n,7722)),name:"PromotionStatistics",meta:{title:"推广统计",icon:"PieChart"}}]},{path:"/identity",component:g,redirect:"/identity/list",name:"Identity",meta:{title:"实名认证管理",icon:"UserFilled"},children:[{path:"list",component:()=>n.e(4167).then(n.bind(n,4167)),name:"IdentityList",meta:{title:"认证列表",icon:"List"}},{path:"detail/:id",component:()=>n.e(6101).then(n.bind(n,6101)),name:"IdentityDetail",meta:{title:"认证详情",icon:"View",activeMenu:"/identity/list"},hidden:!0},{path:"statistics",component:()=>Promise.all([n.e(2032),n.e(4633)]).then(n.bind(n,4633)),name:"IdentityStatistics",meta:{title:"认证统计",icon:"DataAnalysis"}}]},{path:"/:pathMatch(.*)*",redirect:"/404",hidden:!0}],S=(0,i.aE)({history:(0,i.LA)(),routes:v,scrollBehavior:()=>({top:0})}),_=S},2549:(e,t,n)=>{"use strict";n.d(t,{CZ:()=>m,IK:()=>r,TK:()=>d,Vp:()=>s,bz:()=>c,e8:()=>u,iD:()=>o,nu:()=>l,ri:()=>a,wz:()=>p});var i=n(5720);function o(e){return(0,i.A)({url:"/auth/login",method:"post",data:e})}function a(){return(0,i.A)({url:"/auth/logout",method:"post"})}function s(){return(0,i.A)({url:"/auth/status",method:"get"})}function r(e){return(0,i.A)({url:"/system/user/list",method:"get",params:e})}function l(e){return(0,i.A)({url:"/system/user",method:"post",data:e})}function d(e){return(0,i.A)({url:"/system/user",method:"put",data:e})}function c(e){return(0,i.A)({url:"/system/user/"+e,method:"delete"})}function m(e,t){const n={userId:e,password:t};return(0,i.A)({url:"/system/user/resetPwd",method:"put",data:n})}function u(e,t){const n={userId:e,status:t};return(0,i.A)({url:"/system/user/changeStatus",method:"put",data:n})}function p(e){return(0,i.A)({url:"/system/user/"+e,method:"get"})}},2973:(e,t,n)=>{"use strict";var i=n(5130),o=n(6768);const a={id:"app"};function s(e,t,n,i,s,r){const l=(0,o.g2)("router-view");return(0,o.uX)(),(0,o.CE)("div",a,[(0,o.bF)(l)])}const r={name:"App"};var l=n(1241);const d=(0,l.A)(r,[["render",s]]),c=d;var m=n(1579),u=n(7838),p=n(9927),h=(n(4188),n(7477));function f(){const e=e=>{e&&e.message&&e.message.includes("ResizeObserver loop")&&e.stopImmediatePropagation()};return window.addEventListener("error",e,!0),()=>{window.removeEventListener("error",e,!0)}}f();const b=(0,i.Ef)(c);for(const[k,g]of Object.entries(h))b.component(k,g);b.use(u.A),b.use(m.A),b.use(p.A,{size:"default"}),b.mount("#app")},3603:(e,t,n)=>{"use strict";n.r(t),n.d(t,{getAllTokens:()=>m,getLoginType:()=>h,getToken:()=>r,hasToken:()=>c,isAdminLoggedIn:()=>u,isTokenValid:()=>f,isUserLoggedIn:()=>p,refreshToken:()=>b,removeToken:()=>d,setToken:()=>l});var i=n(8704);const o="Admin-Token",a="User-Token",s=7;function r(e="admin_token"){console.log(`[AUTH] Getting token of type: ${e}`);const t="admin_token"===e?o:a,n=i.A.get(t);return console.log("[AUTH] Retrieved token: "+(n?n.substring(0,15)+"...":"null")),n}function l(e,t="admin_token",n=s){console.log(`[AUTH] Setting ${t} token: ${e?e.substring(0,15)+"...":"null"}`);const r="admin_token"===t?o:a;e?(i.A.set(r,e,{expires:n}),console.log(`[AUTH] Token set successfully with expiry of ${n} days`)):console.warn("[AUTH] Attempted to set null or empty token")}function d(e="admin_token"){console.log(`[AUTH] Removing token of type: ${e}`);const t="admin_token"===e?o:a;i.A.remove(t)}function c(e="admin_token"){const t=r(e),n=!!t;return console.log(`[AUTH] Token check for ${e}: ${n?"Valid":"Invalid or missing"}`),n}function m(){const e=r("admin_token"),t=r("user_token");return console.log("[AUTH] Retrieved all tokens:"),console.log("- Admin token: "+(e?"Present":"Missing")),console.log("- User token: "+(t?"Present":"Missing")),{adminToken:e,userToken:t}}function u(){return!!r("admin_token")}function p(){return!!r("user_token")}function h(){return u()?"admin":p()?"user":null}function f(){const e=r();return!!e}function b(){console.log("刷新token")}},5720:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var i=n(788),o=n.n(i),a=n(2933),s=n(1219),r=n(7838),l=n(3603);const d=o().create({baseURL:{NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_BASE_API,timeout:1e4});d.interceptors.request.use((e=>{let t=null;return e.url.includes("/admin/")?(t=(0,l.getToken)("admin_token"),console.log("请求拦截器 - 使用管理员token访问:",e.url)):(t=(0,l.getToken)("user_token"),console.log("请求拦截器 - 使用用户token访问:",e.url)),t?(console.log("使用token:",t?t.substring(0,15)+"...":"null"),e.headers["Authorization"]="Bearer "+t,console.log("设置后的headers:",JSON.stringify(e.headers))):console.log("请求拦截器 - 无token，发送请求到:",e.url),e}),(e=>(console.log(e),Promise.reject(e)))),d.interceptors.response.use((e=>{const t=e.data;return console.log("响应拦截器 - 收到响应:",e.config.url,"状态:",e.status),200!==t.code&&0!==t.code?(401===t.code||50008===t.code||50012===t.code||50014===t.code?a.s.confirm("您已登出，请重新登录","确认登出",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then((()=>{r.A.dispatch("user/resetToken").then((()=>{location.reload()}))})):(0,s.nk)({message:t.message||"Error",type:"error",duration:5e3}),Promise.reject(new Error(t.message||"Error"))):t}),(e=>(console.log("响应拦截器 - 请求错误:",e),e.response&&401===e.response.status?(console.log("收到401错误，token可能无效，重定向到登录页"),(0,s.nk)({message:"登录过期，请重新登录",type:"error",duration:5e3}),r.A.dispatch("user/resetToken").then((()=>{location.reload()}))):(0,s.nk)({message:e.message,type:"error",duration:5e3}),Promise.reject(e))));const c=d},7838:(e,t,n)=>{"use strict";n.d(t,{A:()=>w});var i=n(782),o=n(8704);const a={sidebar:{opened:!o.A.get("sidebarStatus")||!!+o.A.get("sidebarStatus"),withoutAnimation:!1},device:"desktop",size:o.A.get("size")||"default"},s={TOGGLE_SIDEBAR:e=>{e.sidebar.opened=!e.sidebar.opened,e.sidebar.withoutAnimation=!1,e.sidebar.opened?o.A.set("sidebarStatus",1):o.A.set("sidebarStatus",0)},CLOSE_SIDEBAR:(e,t)=>{o.A.set("sidebarStatus",0),e.sidebar.opened=!1,e.sidebar.withoutAnimation=t},TOGGLE_DEVICE:(e,t)=>{e.device=t},SET_SIZE:(e,t)=>{e.size=t,o.A.set("size",t)}},r={toggleSideBar({commit:e}){e("TOGGLE_SIDEBAR")},closeSideBar({commit:e},{withoutAnimation:t}){e("CLOSE_SIDEBAR",t)},toggleDevice({commit:e},t){e("TOGGLE_DEVICE",t)},setSize({commit:e},t){e("SET_SIZE",t)}},l={namespaced:!0,state:a,mutations:s,actions:r};var d=n(2549),c=n(3603);const m={state:{token:(0,c.getToken)(),name:"",avatar:"",roles:[],permissions:[]},mutations:{SET_TOKEN:(e,t)=>{e.token=t},SET_NAME:(e,t)=>{e.name=t},SET_AVATAR:(e,t)=>{e.avatar=t},SET_ROLES:(e,t)=>{e.roles=t},SET_PERMISSIONS:(e,t)=>{e.permissions=t},SET_INTRODUCTION:(e,t)=>{e.introduction=t}},actions:{login({commit:e},t){const{username:n,password:i}=t;return new Promise(((t,o)=>{console.log("[User Store] 开始登录, 用户名:",n),(0,d.iD)({username:n.trim(),password:i}).then((n=>{console.log("[User Store] 登录成功, 服务端返回:",n);const{data:i}=n;if(!i||!i.token)return console.error("[User Store] 登录返回数据中缺少token"),void o(new Error("登录失败: 服务器未返回有效token"));const a=i.roles&&i.roles.includes("admin")?"admin_token":"user_token";console.log(`[User Store] 设置${a}, token值:`,i.token.substring(0,15)+"..."),e("SET_TOKEN",i.token),(0,c.setToken)(i.token,a),setTimeout((()=>{console.log("[User Store] 登录后token状态检查:"),console.log("- Admin Token:",(0,c.getToken)("admin_token")?"已设置":"未设置"),console.log("- User Token:",(0,c.getToken)("user_token")?"已设置":"未设置")}),500),t(i)})).catch((e=>{console.error("[User Store] 登录失败:",e),o(e)}))}))},getInfo({commit:e,state:t}){return console.log("====== 获取用户信息 ======"),console.log("当前token:",t.token),new Promise(((t,n)=>{(0,d.Vp)().then((i=>{const{data:o}=i;if(console.log("获取用户信息成功:",o),!o)return void n("获取用户信息失败，请重新登录");const{roles:a,name:s,avatar:r}=o;if(!a||a.length<=0)return console.log("获取用户角色失败，角色信息:",a),void n("角色信息不能为空");console.log("设置用户角色:",a),console.log("设置用户信息:",o),e("SET_ROLES",a),e("SET_NAME",s),e("SET_AVATAR",r),e("SET_INTRODUCTION",o.introduction),t(o)})).catch((e=>{console.error("获取用户信息失败:",e),n(e)}))}))},logout({commit:e,state:t}){return new Promise(((t,n)=>{(0,d.ri)().then((()=>{e("SET_TOKEN",""),e("SET_ROLES",[]),e("SET_PERMISSIONS",[]),(0,c.removeToken)(),t()})).catch((e=>{n(e)}))}))},fedLogout({commit:e}){return new Promise((t=>{e("SET_TOKEN",""),(0,c.removeToken)(),t()}))}}},u=m;var p=n(5720);function h(e){return(0,p.A)({url:"/admin/auth/login",method:"post",data:e})}function f(){return(0,p.A)({url:"/admin/auth/logout",method:"post"})}function b(){return(0,p.A)({url:"/admin/auth/info",method:"get"})}const k={namespaced:!0,state:{token:(0,c.getToken)("admin_token"),name:"",avatar:"",roles:[],permissions:[]},mutations:{SET_TOKEN:(e,t)=>{e.token=t},SET_NAME:(e,t)=>{e.name=t},SET_AVATAR:(e,t)=>{e.avatar=t},SET_ROLES:(e,t)=>{e.roles=t},SET_PERMISSIONS:(e,t)=>{e.permissions=t}},actions:{login({commit:e},t){const{username:n,password:i}=t;return new Promise(((t,o)=>{h({username:n,password:i}).then((n=>{if(0===n.code&&n.data&&n.data.token){console.log("管理员登录成功:",n.data);const i=n.data.token;if((0,c.setToken)(i,"admin_token"),e("SET_TOKEN",i),n.data.admin){const t=n.data.admin;e("SET_NAME",t.nickname||t.phone||""),e("SET_AVATAR",t.avatar||""),t.roles&&t.roles.length>0?e("SET_ROLES",t.roles):e("SET_ROLES",["admin"]),t.permissions&&e("SET_PERMISSIONS",t.permissions)}t(n)}else o(new Error(n.message||"管理员登录失败"))})).catch((e=>{o(e)}))}))},getInfo({commit:e,state:t}){return console.log("====== 获取管理员信息 ======"),console.log("当前管理员token:",t.token),new Promise(((t,n)=>{b().then((i=>{const{data:o}=i;if(console.log("获取管理员信息成功:",o),!o)return void n("获取管理员信息失败，请重新登录");const{roles:a,nickname:s,avatar:r,permissions:l}=o;if(!a||a.length<=0)return console.log("获取管理员角色失败:",a),void n("管理员角色信息不能为空");console.log("设置管理员角色:",a),console.log("设置管理员信息:",o),e("SET_ROLES",a),e("SET_NAME",s||o.phone||""),e("SET_AVATAR",r||""),l&&e("SET_PERMISSIONS",l),t(o)})).catch((e=>{console.error("获取管理员信息失败:",e),n(e)}))}))},logout({commit:e,state:t}){return new Promise(((t,n)=>{f().then((()=>{e("SET_TOKEN",""),e("SET_ROLES",[]),e("SET_PERMISSIONS",[]),(0,c.removeToken)("admin_token"),t()})).catch((e=>{n(e)}))}))},fedLogout({commit:e}){return new Promise((t=>{e("SET_TOKEN",""),e("SET_ROLES",[]),e("SET_PERMISSIONS",[]),(0,c.removeToken)("admin_token"),t()}))},resetToken({commit:e}){return new Promise((t=>{e("SET_TOKEN",""),e("SET_ROLES",[]),e("SET_PERMISSIONS",[]),(0,c.removeToken)("admin_token"),t()}))}}},g=k;var v=n(1579);const S={state:{routes:[],addRoutes:[]},mutations:{SET_ROUTES:(e,t)=>{e.addRoutes=t,e.routes=v.b.concat(t)}},actions:{generateRoutes({commit:e}){return new Promise((t=>{const n=v.b;e("SET_ROUTES",[]),t(n)}))}}},_=S;var T=n(9963),E=n.n(T);const L={sidebar:e=>e.app.sidebar,size:e=>e.app.size,device:e=>e.app.device,visitedViews:e=>e.tagsView.visitedViews,cachedViews:e=>e.tagsView.cachedViews,token:e=>e.user.token,avatar:e=>e.user.avatar,name:e=>e.user.name,introduction:e=>e.user.introduction,roles:e=>e.user.roles,permissions:e=>e.user.permissions,routes:e=>e.permission.routes,adminToken:e=>e.admin.token,adminAvatar:e=>e.admin.avatar,adminName:e=>e.admin.name,adminRoles:e=>e.admin.roles,adminPermissions:e=>e.admin.permissions,isAdmin:e=>e.admin.token&&e.admin.roles&&(e.admin.roles.includes("admin")||e.admin.roles.includes("superadmin"))},A=L,y=(0,i.y$)({modules:{app:l,user:u,admin:g,permission:_,tagsView:E()},getters:A}),w=y},9963:()=>{}},t={};function n(i){var o=t[i];if(void 0!==o)return o.exports;var a=t[i]={exports:{}};return e[i].call(a.exports,a,a.exports,n),a.exports}n.m=e,(()=>{var e=[];n.O=(t,i,o,a)=>{if(!i){var s=1/0;for(c=0;c<e.length;c++){for(var[i,o,a]=e[c],r=!0,l=0;l<i.length;l++)(!1&a||s>=a)&&Object.keys(n.O).every((e=>n.O[e](i[l])))?i.splice(l--,1):(r=!1,a<s&&(s=a));if(r){e.splice(c--,1);var d=o();void 0!==d&&(t=d)}}return t}a=a||0;for(var c=e.length;c>0&&e[c-1][2]>a;c--)e[c]=e[c-1];e[c]=[i,o,a]}})(),(()=>{n.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return n.d(t,{a:t}),t}})(),(()=>{n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}})(),(()=>{n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,i)=>(n.f[i](e,t),t)),[]))})(),(()=>{n.u=e=>"js/"+e+"."+{865:"7390a71d",1196:"6290a728",1327:"1d2e6086",1669:"e3944e78",1674:"dca4a591",1871:"a9f4d3cd",2032:"75fa505d",2045:"cd5f0a1e",2508:"ff6a988f",2520:"f18e19bb",2672:"2e176911",2917:"3dc79c4a",2977:"6f28a4a5",3758:"0c39abfd",4076:"c4eaa254",4167:"be5f8ad7",4633:"f1f8bbdd",4685:"c0409057",5285:"a735f482",5329:"a9beb581",5382:"44452b83",5757:"a01e4a27",5772:"1a58e249",5882:"300c9640",5903:"b8d65e03",6074:"62f21075",6101:"4fcb1c66",6610:"712bb9f4",6653:"e9ac97a1",6675:"f880d0f4",6955:"baaf1d9d",6960:"9614758a",7232:"f407f396",7550:"9206d275",7722:"e2934725",8111:"30019e04",8258:"902d6f97",8504:"f544c98c",8591:"847fbc4c",8709:"eb0ec49f",8945:"12f09a55",9273:"ee8bf39e",9298:"cfa2926a",9374:"721673e6",9414:"71b7985e",9629:"74f31cb6",9641:"e68dfbda",9746:"df7f1551",9954:"dabad49c"}[e]+".js"})(),(()=>{n.miniCssF=e=>"css/"+e+"."+{865:"5a78d437",1327:"309030ac",1669:"5500b72c",1674:"d8d9fc2e",1871:"0780158b",2045:"de671e1d",2508:"45935be1",2520:"20d59d6d",2917:"9e720526",2977:"b68e05af",3758:"c99068b9",4076:"f0e60090",4167:"85b81707",4633:"97d339ea",4685:"761e17a9",5285:"50c03b80",5329:"34c0d69a",5382:"6fede4e7",5757:"457cdc19",5772:"497b9471",5882:"f0b27126",5903:"5189a106",6074:"b11f4ee5",6101:"27398723",6675:"06f482bc",6955:"b9f35c1b",6960:"1dc5a122",7232:"3e7b243f",7550:"a60b68da",7722:"70d3eb8b",8111:"a634648c",8258:"78551b2e",8504:"11cd4718",8591:"8f51910b",8709:"3bfe9910",8945:"55c051f6",9273:"0a5f00f2",9298:"2e6071d4",9374:"3d0992ea",9414:"b985a7fc",9629:"a4a3b07b",9641:"211f8e26",9746:"5cb62da2",9954:"6b836bf7"}[e]+".css"})(),(()=>{n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{var e={},t="yzb-admin-system:";n.l=(i,o,a,s)=>{if(e[i])e[i].push(o);else{var r,l;if(void 0!==a)for(var d=document.getElementsByTagName("script"),c=0;c<d.length;c++){var m=d[c];if(m.getAttribute("src")==i||m.getAttribute("data-webpack")==t+a){r=m;break}}r||(l=!0,r=document.createElement("script"),r.charset="utf-8",r.timeout=120,n.nc&&r.setAttribute("nonce",n.nc),r.setAttribute("data-webpack",t+a),r.src=i),e[i]=[o];var u=(t,n)=>{r.onerror=r.onload=null,clearTimeout(p);var o=e[i];if(delete e[i],r.parentNode&&r.parentNode.removeChild(r),o&&o.forEach((e=>e(n))),t)return t(n)},p=setTimeout(u.bind(null,void 0,{type:"timeout",target:r}),12e4);r.onerror=u.bind(null,r.onerror),r.onload=u.bind(null,r.onload),l&&document.head.appendChild(r)}}})(),(()=>{n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{n.p="/"})(),(()=>{if("undefined"!==typeof document){var e=(e,t,i,o,a)=>{var s=document.createElement("link");s.rel="stylesheet",s.type="text/css",n.nc&&(s.nonce=n.nc);var r=n=>{if(s.onerror=s.onload=null,"load"===n.type)o();else{var i=n&&n.type,r=n&&n.target&&n.target.href||t,l=new Error("Loading CSS chunk "+e+" failed.\n("+i+": "+r+")");l.name="ChunkLoadError",l.code="CSS_CHUNK_LOAD_FAILED",l.type=i,l.request=r,s.parentNode&&s.parentNode.removeChild(s),a(l)}};return s.onerror=s.onload=r,s.href=t,i?i.parentNode.insertBefore(s,i.nextSibling):document.head.appendChild(s),s},t=(e,t)=>{for(var n=document.getElementsByTagName("link"),i=0;i<n.length;i++){var o=n[i],a=o.getAttribute("data-href")||o.getAttribute("href");if("stylesheet"===o.rel&&(a===e||a===t))return o}var s=document.getElementsByTagName("style");for(i=0;i<s.length;i++){o=s[i],a=o.getAttribute("data-href");if(a===e||a===t)return o}},i=i=>new Promise(((o,a)=>{var s=n.miniCssF(i),r=n.p+s;if(t(s,r))return o();e(i,r,null,o,a)})),o={3524:0};n.f.miniCss=(e,t)=>{var n={865:1,1327:1,1669:1,1674:1,1871:1,2045:1,2508:1,2520:1,2917:1,2977:1,3758:1,4076:1,4167:1,4633:1,4685:1,5285:1,5329:1,5382:1,5757:1,5772:1,5882:1,5903:1,6074:1,6101:1,6675:1,6955:1,6960:1,7232:1,7550:1,7722:1,8111:1,8258:1,8504:1,8591:1,8709:1,8945:1,9273:1,9298:1,9374:1,9414:1,9629:1,9641:1,9746:1,9954:1};o[e]?t.push(o[e]):0!==o[e]&&n[e]&&t.push(o[e]=i(e).then((()=>{o[e]=0}),(t=>{throw delete o[e],t})))}}})(),(()=>{var e={3524:0};n.f.j=(t,i)=>{var o=n.o(e,t)?e[t]:void 0;if(0!==o)if(o)i.push(o[2]);else{var a=new Promise(((n,i)=>o=e[t]=[n,i]));i.push(o[2]=a);var s=n.p+n.u(t),r=new Error,l=i=>{if(n.o(e,t)&&(o=e[t],0!==o&&(e[t]=void 0),o)){var a=i&&("load"===i.type?"missing":i.type),s=i&&i.target&&i.target.src;r.message="Loading chunk "+t+" failed.\n("+a+": "+s+")",r.name="ChunkLoadError",r.type=a,r.request=s,o[1](r)}};n.l(s,l,"chunk-"+t,t)}},n.O.j=t=>0===e[t];var t=(t,i)=>{var o,a,[s,r,l]=i,d=0;if(s.some((t=>0!==e[t]))){for(o in r)n.o(r,o)&&(n.m[o]=r[o]);if(l)var c=l(n)}for(t&&t(i);d<s.length;d++)a=s[d],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return n.O(c)},i=self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))})();var i=n.O(void 0,[504],(()=>n(2973)));i=n.O(i)})();