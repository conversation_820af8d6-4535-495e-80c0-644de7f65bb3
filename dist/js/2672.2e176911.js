"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2672],{2672:(t,e,n)=>{n.r(e),n.d(e,{addStation:()=>r,auditStation:()=>d,deleteStation:()=>u,exportStation:()=>c,getStationDetail:()=>o,getStationList:()=>a,getStationStatistics:()=>f,getVerificationDetail:()=>h,getVerificationList:()=>p,reviewVerification:()=>A,testDeleteStation:()=>m,updateStation:()=>s,updateStationStatus:()=>l});var i=n(5720);function a(t){return(0,i.A)({url:"/stations/stations",method:"get",params:t})}function o(t){return(0,i.A)({url:`/stations/stations/${t}`,method:"get"})}function r(t){return(0,i.A)({url:"/station",method:"post",data:t})}function s(t){return(0,i.A)({url:"/station",method:"put",data:t})}function u(t){return(0,i.A)({url:`/stations/admin/stations/${t}`,method:"delete"})}function d(t){return(0,i.A)({url:"/station/audit",method:"put",data:t})}function l(t){return(0,i.A)({url:"/station/status",method:"put",data:t})}function c(t){return(0,i.A)({url:"/station/export",method:"get",params:t,responseType:"blob"})}function f(){return(0,i.A)({url:"/station/statistics",method:"get"})}function m(t){return(0,i.A)({url:`/stations/delete-station/${t}`,method:"delete"})}function p(t){return(0,i.A)({url:"/station/verify/list",method:"get",params:t})}function h(t){return(0,i.A)({url:`/station/verify/${t}`,method:"get"})}function A(t,e){return(0,i.A)({url:`/station/verify/${t}/review`,method:"post",data:e})}}}]);