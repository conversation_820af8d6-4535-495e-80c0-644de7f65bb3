"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2672,6675],{1981:(e,t,a)=>{function i(e,t="YYYY-MM-DD HH:mm:ss"){if(!e)return"";let a;"object"===typeof e?a=e:("string"===typeof e&&/^[0-9]+$/.test(e)&&(e=parseInt(e)),"number"===typeof e&&10===e.toString().length&&(e*=1e3),a=new Date(e));const i={"M+":a.getMonth()+1,"D+":a.getDate(),"H+":a.getHours(),"m+":a.getMinutes(),"s+":a.getSeconds(),"q+":Math.floor((a.getMonth()+3)/3),S:a.getMilliseconds()};/(Y+)/.test(t)&&(t=t.replace(RegExp.$1,(a.getFullYear()+"").substr(4-RegExp.$1.length)));for(let l in i)new RegExp("("+l+")").test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?i[l]:("00"+i[l]).substr((""+i[l]).length)));return t}a.d(t,{r6:()=>i})},2672:(e,t,a)=>{a.r(t),a.d(t,{addStation:()=>n,auditStation:()=>d,deleteStation:()=>r,exportStation:()=>c,getStationDetail:()=>s,getStationList:()=>l,getStationStatistics:()=>p,getVerificationDetail:()=>m,getVerificationList:()=>v,reviewVerification:()=>y,testDeleteStation:()=>g,updateStation:()=>o,updateStationStatus:()=>u});var i=a(5720);function l(e){return(0,i.A)({url:"/stations/stations",method:"get",params:e})}function s(e){return(0,i.A)({url:`/stations/stations/${e}`,method:"get"})}function n(e){return(0,i.A)({url:"/station",method:"post",data:e})}function o(e){return(0,i.A)({url:"/station",method:"put",data:e})}function r(e){return(0,i.A)({url:`/stations/admin/stations/${e}`,method:"delete"})}function d(e){return(0,i.A)({url:"/station/audit",method:"put",data:e})}function u(e){return(0,i.A)({url:"/station/status",method:"put",data:e})}function c(e){return(0,i.A)({url:"/station/export",method:"get",params:e,responseType:"blob"})}function p(){return(0,i.A)({url:"/station/statistics",method:"get"})}function g(e){return(0,i.A)({url:`/stations/delete-station/${e}`,method:"delete"})}function v(e){return(0,i.A)({url:"/station/verify/list",method:"get",params:e})}function m(e){return(0,i.A)({url:`/station/verify/${e}`,method:"get"})}function y(e,t){return(0,i.A)({url:`/station/verify/${e}/review`,method:"post",data:t})}},6675:(e,t,a)=>{a.r(t),a.d(t,{default:()=>ue});var i=a(6768),l=a(5130),s=a(4232);const n={class:"app-container"},o={class:"filter-item"},r={class:"table-header"},d={class:"statistics-cards"},u={class:"stat-card primary"},c={class:"stat-value"},p={class:"stat-card warning"},g={class:"stat-value"},v={class:"stat-card success"},m={class:"stat-value"},y={class:"stat-card danger"},h={class:"stat-value"},k={class:"station-info"},f={key:1,class:"no-image",style:{width:"60px",height:"60px","border-radius":"4px","background-color":"#f0f2f5",display:"flex","align-items":"center","justify-content":"center"}},b={class:"station-detail"},w={class:"station-name"},_={class:"station-address"},S={class:"contact-phone"},C={key:1},A={class:"user-info"},F={class:"user-detail"},L={key:0},x={key:1},V={class:"station-detail-dialog"},T={class:"image-container"},I={class:"image-section"},P={class:"image-preview"},W={key:1,class:"no-image"},D={class:"image-section"},$={class:"image-preview"},z={key:1,class:"no-image"},X={class:"image-container"},R={class:"image-section"},Q={class:"image-preview"},j={key:1,class:"no-image"},K={class:"image-section"},E={class:"image-preview"},U={key:1,class:"no-image"},M={class:"dialog-footer"},N={class:"dialog-footer"},O={class:"image-preview-container"};function Y(e,t,a,Y,q,H){const B=(0,i.g2)("el-input"),G=(0,i.g2)("el-option"),J=(0,i.g2)("el-select"),Z=(0,i.g2)("el-date-picker"),ee=(0,i.g2)("el-button"),te=(0,i.g2)("el-card"),ae=(0,i.g2)("el-table-column"),ie=(0,i.g2)("el-image"),le=(0,i.g2)("Picture"),se=(0,i.g2)("el-icon"),ne=(0,i.g2)("el-tag"),oe=(0,i.g2)("el-avatar"),re=(0,i.g2)("el-table"),de=(0,i.g2)("pagination"),ue=(0,i.g2)("el-descriptions-item"),ce=(0,i.g2)("el-descriptions"),pe=(0,i.g2)("el-dialog"),ge=(0,i.g2)("el-form-item"),ve=(0,i.g2)("el-form"),me=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)("div",n,[(0,i.bF)(te,{class:"filter-container"},{default:(0,i.k6)((()=>[(0,i.Lk)("div",o,[(0,i.bF)(B,{modelValue:Y.listQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>Y.listQuery.keyword=e),placeholder:"驿站名称/联系人",clearable:"",onKeyup:(0,l.jR)(Y.handleFilter,["enter"]),style:{width:"200px"}},null,8,["modelValue","onKeyup"]),(0,i.bF)(J,{modelValue:Y.listQuery.status,"onUpdate:modelValue":t[1]||(t[1]=e=>Y.listQuery.status=e),placeholder:"审核状态",clearable:"",style:{width:"130px"},onChange:Y.handleFilter},{default:(0,i.k6)((()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(Y.statusOptions,(e=>((0,i.uX)(),(0,i.Wv)(G,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,i.bF)(J,{modelValue:Y.listQuery.type,"onUpdate:modelValue":t[2]||(t[2]=e=>Y.listQuery.type=e),placeholder:"驿站类型",clearable:"",style:{width:"130px"},onChange:Y.handleFilter},{default:(0,i.k6)((()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(Y.typeOptions,(e=>((0,i.uX)(),(0,i.Wv)(G,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,i.bF)(Z,{modelValue:Y.dateRange,"onUpdate:modelValue":t[3]||(t[3]=e=>Y.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",style:{width:"260px"},onChange:Y.handleDateChange},null,8,["modelValue","onChange"]),(0,i.bF)(ee,{type:"primary",icon:"Search",onClick:Y.handleFilter},{default:(0,i.k6)((()=>t[12]||(t[12]=[(0,i.eW)("搜索")]))),_:1},8,["onClick"]),(0,i.bF)(ee,{icon:"Refresh",onClick:Y.resetQuery},{default:(0,i.k6)((()=>t[13]||(t[13]=[(0,i.eW)("重置")]))),_:1},8,["onClick"])])])),_:1}),(0,i.bF)(te,null,{default:(0,i.k6)((()=>[(0,i.Lk)("div",r,[(0,i.Lk)("div",d,[(0,i.Lk)("div",u,[(0,i.Lk)("div",c,(0,s.v_)(Y.statistics.total||0),1),t[14]||(t[14]=(0,i.Lk)("div",{class:"stat-label"},"总申请数",-1))]),(0,i.Lk)("div",p,[(0,i.Lk)("div",g,(0,s.v_)(Y.statistics.pending||0),1),t[15]||(t[15]=(0,i.Lk)("div",{class:"stat-label"},"待审核",-1))]),(0,i.Lk)("div",v,[(0,i.Lk)("div",m,(0,s.v_)(Y.statistics.approved||0),1),t[16]||(t[16]=(0,i.Lk)("div",{class:"stat-label"},"已通过",-1))]),(0,i.Lk)("div",y,[(0,i.Lk)("div",h,(0,s.v_)(Y.statistics.rejected||0),1),t[17]||(t[17]=(0,i.Lk)("div",{class:"stat-label"},"已拒绝",-1))])]),(0,i.Lk)("div",null,[(0,i.bF)(ee,{type:"success",size:"small",onClick:Y.exportData},{default:(0,i.k6)((()=>t[18]||(t[18]=[(0,i.eW)("导出数据")]))),_:1},8,["onClick"])])]),(0,i.bo)(((0,i.uX)(),(0,i.Wv)(re,{data:Y.list,border:"",fit:"","highlight-current-row":"",style:{width:"100%"}},{default:(0,i.k6)((()=>[(0,i.bF)(ae,{label:"ID",prop:"id",align:"center",width:"80"}),(0,i.bF)(ae,{label:"驿站信息","min-width":"200"},{default:(0,i.k6)((({row:e})=>[(0,i.Lk)("div",k,[e.storefront?((0,i.uX)(),(0,i.Wv)(ie,{key:0,src:e.storefront,style:{width:"60px",height:"60px","border-radius":"4px"}},null,8,["src"])):((0,i.uX)(),(0,i.CE)("div",f,[(0,i.bF)(se,null,{default:(0,i.k6)((()=>[(0,i.bF)(le)])),_:1})])),(0,i.Lk)("div",b,[(0,i.Lk)("div",w,(0,s.v_)(e.stationName),1),(0,i.Lk)("div",_,(0,s.v_)(e.address),1),(0,i.Lk)("div",null,[(0,i.bF)(ne,{size:"small",type:Y.getTypeTagType(e.stationType)},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.getTypeText(e.stationType)),1)])),_:2},1032,["type"])])])])])),_:1}),(0,i.bF)(ae,{label:"联系人",align:"center",width:"150"},{default:(0,i.k6)((({row:e})=>[(0,i.Lk)("div",null,(0,s.v_)(e.contactName),1),(0,i.Lk)("div",S,(0,s.v_)(e.contactPhone),1)])),_:1}),(0,i.bF)(ae,{label:"营业执照",align:"center",width:"120"},{default:(0,i.k6)((({row:e})=>[e.businessLicense?((0,i.uX)(),(0,i.Wv)(ee,{key:0,size:"small",type:"primary",plain:"",onClick:t=>Y.previewImage(e.businessLicense)},{default:(0,i.k6)((()=>t[19]||(t[19]=[(0,i.eW)("查看")]))),_:2},1032,["onClick"])):((0,i.uX)(),(0,i.CE)("span",C,"无"))])),_:1}),(0,i.bF)(ae,{label:"申请人",align:"center",width:"150"},{default:(0,i.k6)((({row:e})=>[(0,i.Lk)("div",A,[e.user&&e.user.avatar?((0,i.uX)(),(0,i.Wv)(oe,{key:0,src:e.user.avatar,size:30},null,8,["src"])):((0,i.uX)(),(0,i.Wv)(oe,{key:1,size:30,icon:"User"})),(0,i.Lk)("div",F,[(0,i.Lk)("div",null,(0,s.v_)(e.user?e.user.nickname||"未设置昵称":"未知用户"),1),(0,i.Lk)("div",null,(0,s.v_)(e.user?e.user.phone:""),1)])])])),_:1}),(0,i.bF)(ae,{label:"申请时间",width:"150",align:"center"},{default:(0,i.k6)((({row:e})=>[(0,i.Lk)("span",null,(0,s.v_)(Y.formatDateTime(e.createdAt)),1)])),_:1}),(0,i.bF)(ae,{label:"状态",align:"center",width:"100"},{default:(0,i.k6)((({row:e})=>[(0,i.bF)(ne,{type:Y.getStatusType(e.status)},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(e.statusText||Y.getStatusText(e.status)),1)])),_:2},1032,["type"])])),_:1}),(0,i.bF)(ae,{label:"审核时间",width:"150",align:"center"},{default:(0,i.k6)((({row:e})=>[e.reviewedAt?((0,i.uX)(),(0,i.CE)("span",L,(0,s.v_)(Y.formatDateTime(e.reviewedAt)),1)):((0,i.uX)(),(0,i.CE)("span",x,"--"))])),_:1}),(0,i.bF)(ae,{label:"操作",align:"center",width:"200","class-name":"small-padding fixed-width"},{default:(0,i.k6)((({row:e})=>["pending"===e.status?((0,i.uX)(),(0,i.Wv)(ee,{key:0,type:"success",size:"small",onClick:t=>Y.handleApprove(e)},{default:(0,i.k6)((()=>t[20]||(t[20]=[(0,i.eW)("通过")]))),_:2},1032,["onClick"])):(0,i.Q3)("",!0),"pending"===e.status?((0,i.uX)(),(0,i.Wv)(ee,{key:1,type:"danger",size:"small",onClick:t=>Y.handleReject(e)},{default:(0,i.k6)((()=>t[21]||(t[21]=[(0,i.eW)("拒绝")]))),_:2},1032,["onClick"])):(0,i.Q3)("",!0),(0,i.bF)(ee,{type:"info",size:"small",onClick:t=>Y.handleDetail(e)},{default:(0,i.k6)((()=>t[22]||(t[22]=[(0,i.eW)("详情")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[me,Y.listLoading]]),(0,i.bo)((0,i.bF)(de,{total:Y.total,page:Y.listQuery.page,limit:Y.listQuery.limit,onPagination:e.getList},null,8,["total","page","limit","onPagination"]),[[l.aG,Y.total>0]])])),_:1}),(0,i.bF)(pe,{title:"驿站认证详情",modelValue:Y.dialogVisible,"onUpdate:modelValue":t[7]||(t[7]=e=>Y.dialogVisible=e),width:"700px"},{default:(0,i.k6)((()=>[(0,i.bo)(((0,i.uX)(),(0,i.CE)("div",V,[(0,i.bF)(ce,{column:2,border:""},{default:(0,i.k6)((()=>[(0,i.bF)(ue,{label:"申请编号",span:2},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.detail.id),1)])),_:1}),(0,i.bF)(ue,{label:"驿站名称",span:2},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.detail.stationName),1)])),_:1}),(0,i.bF)(ue,{label:"驿站类型"},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.getTypeText(Y.detail.stationType)),1)])),_:1}),(0,i.bF)(ue,{label:"品牌"},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.detail.brand||"--"),1)])),_:1}),(0,i.bF)(ue,{label:"联系人"},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.detail.contactName),1)])),_:1}),(0,i.bF)(ue,{label:"联系电话"},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.detail.contactPhone),1)])),_:1}),(0,i.bF)(ue,{label:"详细地址",span:2},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.detail.address),1)])),_:1}),(0,i.bF)(ue,{label:"申请时间"},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.formatDateTime(Y.detail.createdAt)),1)])),_:1}),(0,i.bF)(ue,{label:"审核时间"},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.detail.reviewedAt?Y.formatDateTime(Y.detail.reviewedAt):"--"),1)])),_:1}),(0,i.bF)(ue,{label:"状态"},{default:(0,i.k6)((()=>[void 0!==Y.detail.status?((0,i.uX)(),(0,i.Wv)(ne,{key:0,type:Y.getStatusType(Y.detail.status)},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.detail.statusText||Y.getStatusText(Y.detail.status)),1)])),_:1},8,["type"])):(0,i.Q3)("",!0)])),_:1}),(0,i.bF)(ue,{label:"审核备注"},{default:(0,i.k6)((()=>[(0,i.eW)((0,s.v_)(Y.detail.remark||"--"),1)])),_:1})])),_:1}),(0,i.Lk)("div",T,[(0,i.Lk)("div",I,[t[23]||(t[23]=(0,i.Lk)("div",{class:"image-title"},"门头照",-1)),(0,i.Lk)("div",P,[Y.detail.storefront?((0,i.uX)(),(0,i.Wv)(ie,{key:0,src:Y.detail.storefront,fit:"cover","preview-src-list":[Y.detail.storefront],style:{width:"200px",height:"150px","border-radius":"4px"}},null,8,["src","preview-src-list"])):((0,i.uX)(),(0,i.CE)("div",W,"未上传照片"))])]),(0,i.Lk)("div",D,[t[24]||(t[24]=(0,i.Lk)("div",{class:"image-title"},"内部照",-1)),(0,i.Lk)("div",$,[Y.detail.interior?((0,i.uX)(),(0,i.Wv)(ie,{key:0,src:Y.detail.interior,fit:"cover","preview-src-list":[Y.detail.interior],style:{width:"200px",height:"150px","border-radius":"4px"}},null,8,["src","preview-src-list"])):((0,i.uX)(),(0,i.CE)("div",z,"未上传照片"))])])]),(0,i.Lk)("div",X,[(0,i.Lk)("div",R,[t[25]||(t[25]=(0,i.Lk)("div",{class:"image-title"},"营业执照",-1)),(0,i.Lk)("div",Q,[Y.detail.businessLicense?((0,i.uX)(),(0,i.Wv)(ie,{key:0,src:Y.detail.businessLicense,fit:"cover","preview-src-list":[Y.detail.businessLicense],style:{width:"200px",height:"150px","border-radius":"4px"}},null,8,["src","preview-src-list"])):((0,i.uX)(),(0,i.CE)("div",j,"未上传执照"))])]),(0,i.Lk)("div",K,[t[26]||(t[26]=(0,i.Lk)("div",{class:"image-title"},"授权书",-1)),(0,i.Lk)("div",E,[Y.detail.authorization?((0,i.uX)(),(0,i.Wv)(ie,{key:0,src:Y.detail.authorization,fit:"cover","preview-src-list":[Y.detail.authorization],style:{width:"200px",height:"150px","border-radius":"4px"}},null,8,["src","preview-src-list"])):((0,i.uX)(),(0,i.CE)("div",U,"未上传授权书"))])])]),(0,i.Lk)("div",M,[(0,i.bF)(ee,{onClick:t[4]||(t[4]=e=>Y.dialogVisible=!1)},{default:(0,i.k6)((()=>t[27]||(t[27]=[(0,i.eW)("关闭")]))),_:1}),"pending"===Y.detail.status?((0,i.uX)(),(0,i.CE)(i.FK,{key:0},[(0,i.bF)(ee,{type:"success",onClick:t[5]||(t[5]=e=>Y.handleApprove(Y.detail))},{default:(0,i.k6)((()=>t[28]||(t[28]=[(0,i.eW)("通过")]))),_:1}),(0,i.bF)(ee,{type:"danger",onClick:t[6]||(t[6]=e=>Y.handleReject(Y.detail))},{default:(0,i.k6)((()=>t[29]||(t[29]=[(0,i.eW)("拒绝")]))),_:1})],64)):(0,i.Q3)("",!0)])])),[[me,Y.detailLoading]])])),_:1},8,["modelValue"]),(0,i.bF)(pe,{title:"approve"===Y.auditAction?"通过驿站认证":"拒绝驿站认证",modelValue:Y.auditDialogVisible,"onUpdate:modelValue":t[10]||(t[10]=e=>Y.auditDialogVisible=e),width:"500px"},{footer:(0,i.k6)((()=>[(0,i.Lk)("div",N,[(0,i.bF)(ee,{onClick:t[9]||(t[9]=e=>Y.auditDialogVisible=!1)},{default:(0,i.k6)((()=>t[30]||(t[30]=[(0,i.eW)("取 消")]))),_:1}),(0,i.bF)(ee,{type:"approve"===Y.auditAction?"success":"danger",onClick:Y.submitAudit,loading:Y.auditSubmitting},{default:(0,i.k6)((()=>t[31]||(t[31]=[(0,i.eW)("确 定")]))),_:1},8,["type","onClick","loading"])])])),default:(0,i.k6)((()=>[(0,i.bF)(ve,{model:Y.auditForm,"label-width":"80px"},{default:(0,i.k6)((()=>[(0,i.bF)(ge,{label:"审核意见"},{default:(0,i.k6)((()=>[(0,i.bF)(B,{modelValue:Y.auditForm.remark,"onUpdate:modelValue":t[8]||(t[8]=e=>Y.auditForm.remark=e),type:"textarea",rows:4,placeholder:"approve"===Y.auditAction?"审核通过说明（选填）":"拒绝原因（必填）"},null,8,["modelValue","placeholder"])])),_:1})])),_:1},8,["model"])])),_:1},8,["title","modelValue"]),(0,i.bF)(pe,{title:"图片预览",modelValue:Y.imagePreviewVisible,"onUpdate:modelValue":t[11]||(t[11]=e=>Y.imagePreviewVisible=e),width:"700px",center:""},{default:(0,i.k6)((()=>[(0,i.Lk)("div",O,[(0,i.bF)(ie,{src:Y.previewImageUrl,style:{"max-width":"100%","max-height":"500px"}},null,8,["src"])])])),_:1},8,["modelValue"])])}var q=a(144),H=a(1219),B=a(9190),G=a(1981),J=a(2672),Z=a(5720);const ee="v1",te={interval:3e5,modules:["identity","station","promotion","order","user"],realtime:!0,conflictStrategy:"latest-win"};let ae=0;class ie{constructor(){this.syncQueue=[],this.isSyncing=!1,this.lastSyncTime=null,this.syncInterval=null}init(){return te.interval>0&&(this.syncInterval=setInterval((()=>{this.syncAll()}),te.interval)),this.lastSyncTime=new Date,console.log("数据同步服务已初始化",this.lastSyncTime),this}addToQueue(e,t,a,i=!1){if(!te.modules.includes(e))return console.warn(`模块 ${e} 不在同步列表中`),!1;const l={id:`sync_${Date.now()}_${ae++}`,module:e,action:t,data:a,timestamp:Date.now(),retries:0,status:"pending"};return this.syncQueue.push(l),console.log(`添加同步项: ${e}.${t}`,a),(i||te.realtime)&&this.processSyncQueue(),l.id}async processSyncQueue(){if(!this.isSyncing&&0!==this.syncQueue.length){this.isSyncing=!0;try{const e=this.syncQueue.find((e=>"pending"===e.status));if(!e)return void(this.isSyncing=!1);e.status="processing";const t=await this.sendSyncRequest(e);t.success?(e.status="completed",this.syncQueue=this.syncQueue.filter((t=>t.id!==e.id)),console.log(`同步成功: ${e.module}.${e.action}`)):(e.status="failed",e.error=t.error,e.retries+=1,e.retries<3?(e.status="pending",console.warn(`同步失败，将重试: ${e.module}.${e.action}`,t.error)):console.error(`同步失败，已达最大重试次数: ${e.module}.${e.action}`,t.error))}catch(e){console.error("处理同步队列出错:",e)}finally{this.isSyncing=!1,this.syncQueue.some((e=>"pending"===e.status))&&setTimeout((()=>this.processSyncQueue()),1e3)}}}async sendSyncRequest(e){try{const t=`/api/${ee}/sync/${e.module}/${e.action}`,a=await(0,Z.A)({url:t,method:"POST",data:{data:e.data,timestamp:e.timestamp}});return this.lastSyncTime=new Date,{success:!0,data:a.data}}catch(t){return{success:!1,error:t.message||"同步请求失败"}}}async syncAll(){console.log("开始全量同步...");for(const t of te.modules)try{const e=this.lastSyncTime?this.lastSyncTime.toISOString():null,a=await(0,Z.A)({url:`/api/${ee}/sync/${t}/all`,method:"POST",data:{lastSync:e}});console.log(`模块 ${t} 同步完成:`,a.data)}catch(e){console.error(`模块 ${t} 同步失败:`,e)}this.lastSyncTime=new Date,console.log("全量同步完成",this.lastSyncTime)}stop(){this.syncInterval&&(clearInterval(this.syncInterval),this.syncInterval=null),console.log("数据同步服务已停止")}}new ie;function le(e){return console.log("[API] 调用小程序认证列表API, 参数:",e),(0,Z.A)({url:"/admin/miniprogram/verification/getList",method:"post",data:e}).then((e=>(console.log("[API] 小程序认证列表API响应成功:",e),e))).catch((e=>{if(console.error("[API] 小程序认证列表API请求失败:",e),e.response&&(console.error("[API] 响应状态码:",e.response.status),console.error("[API] 响应数据:",e.response.data),401===e.response.status)){const{getToken:e}=a(3603);console.error("[API] 401错误 - 当前Token:",e("admin_token")?"存在":"不存在")}throw e}))}function se(e){return console.log("[API] 调用小程序认证详情API, ID:",e),(0,Z.A)({url:"/admin/miniprogram/verification/getDetail",method:"post",data:{id:e}}).then((e=>(console.log("[API] 小程序认证详情API响应成功:",e),e))).catch((e=>{throw console.error("[API] 小程序认证详情API请求失败:",e),e.response&&(console.error("[API] 响应状态码:",e.response.status),console.error("[API] 响应数据:",e.response.data)),e}))}var ne=a(3603);const oe={name:"StationVerify",components:{Pagination:B.A},setup(){const e=(0,q.KR)([]),t=(0,q.KR)([]),a=(0,q.KR)(0),l=(0,q.KR)(!1),s=(0,q.KR)(!1),n=(0,q.KR)(!1),o=(0,q.KR)({}),r=(0,q.KR)(!1),d=(0,q.KR)(!1),u=(0,q.KR)("approve"),c=(0,q.KR)(!1),p=(0,q.KR)(""),g=(0,q.Kh)({page:1,limit:10,status:"",type:"",keyword:"",start_date:"",end_date:""}),v=(0,q.Kh)({total:0,pending:0,approved:0,rejected:0}),m=(0,q.Kh)({id:null,status:"",remark:""}),y=[{label:"待审核",value:"pending"},{label:"已通过",value:"approved"},{label:"已拒绝",value:"rejected"}],h=[{label:"社区驿站",value:"community"},{label:"快递驿站",value:"express"},{label:"校园驿站",value:"campus"},{label:"写字楼驿站",value:"office"},{label:"商业驿站",value:"commercial"}],k=async()=>{try{l.value=!0,console.log("[Station Verify] 开始获取认证申请列表"),console.log("[Station Verify] 请求参数:",{page:g.page,limit:g.limit,status:g.status||void 0,keyword:g.keyword||void 0});const s=(0,ne.getToken)("admin_token");console.log("[Station Verify] 当前Token状态:",s?"已设置":"未设置");try{const e=await(0,J.getVerificationList)({page:g.page,limit:g.limit,status:g.status||void 0,keyword:g.keyword||void 0});console.log("[Station Verify] 获取成功 - 常规API:",e),t.value=e.data.list,a.value=e.data.total,e.data.statusCount&&(v.total=e.data.statusCount.total,v.pending=e.data.statusCount.pending,v.approved=e.data.statusCount.approved,v.rejected=e.data.statusCount.rejected),l.value=!1}catch(e){console.error("[Station Verify] 常规API获取失败, 尝试小程序API:",e);try{const e=await le({page:g.page,limit:g.limit,status:g.status||void 0,keyword:g.keyword||void 0});console.log("[Station Verify] 获取成功 - 小程序API:",e),t.value=e.data.list,a.value=e.data.total,e.data.statusCount&&(v.total=e.data.statusCount.total,v.pending=e.data.statusCount.pending,v.approved=e.data.statusCount.approved,v.rejected=e.data.statusCount.rejected)}catch(i){console.error("[Station Verify] 两种API都获取失败:",i),H.nk.error("获取认证申请列表失败: "+(i.message||"未知错误"))}finally{l.value=!1}}}catch(e){console.error("[Station Verify] 获取列表时发生异常:",e),l.value=!1,H.nk.error("获取认证申请列表失败: "+(e.message||"未知错误"))}},f=()=>{g.page=1,k()},b=()=>{e.value=[],Object.assign(g,{page:1,limit:10,status:"",type:"",keyword:"",start_date:"",end_date:""}),k()},w=e=>{e?(g.start_date=e[0],g.end_date=e[1]):(g.start_date="",g.end_date="")},_=e=>{const t={pending:"warning",approved:"success",rejected:"danger"};return t[e]||"info"},S=e=>{const t={pending:"待审核",approved:"已通过",rejected:"已拒绝"};return t[e]||"未知"},C=e=>{const t={community:"社区驿站",express:"快递驿站",campus:"校园驿站",office:"写字楼驿站",commercial:"商业驿站"};return t[e]||"未知"},A=e=>{const t={community:"",express:"success",campus:"warning",office:"info",commercial:"danger"};return t[e]||""},F=e=>{n.value=!0,s.value=!0,(0,J.getVerificationDetail)(e.id).then((t=>{0===t.code?(o.value=t.data,n.value=!1):(console.error("原始API获取详情失败:",t.message),L(e.id))})).catch((t=>{console.error("获取认证申请详情失败:",t),L(e.id)}))},L=e=>{se(e).then((e=>{0===e.code?(o.value=e.data,n.value=!1):(H.nk.error(e.message||"获取认证申请详情失败"),n.value=!1)})).catch((e=>{console.error("小程序API获取详情也失败了:",e),H.nk.error("获取认证申请详情失败"),n.value=!1}))},x=e=>{p.value=e,c.value=!0},V=e=>{u.value="approve",m.id=e.id,m.status="approved",m.remark="",r.value=!0},T=e=>{u.value="reject",m.id=e.id,m.status="rejected",m.remark="",r.value=!0},I=()=>{"reject"!==u.value||m.remark?(d.value=!0,(0,J.reviewVerification)(m.id,{status:m.status,remark:m.remark}).then((e=>{0===e.code?((0,H.nk)({type:"success",message:"approve"===u.value?"驿站认证已通过":"驿站认证已拒绝"}),r.value=!1,s.value&&o.value.id===m.id&&(s.value=!1),k()):H.nk.error(e.message||"审核操作失败"),d.value=!1})).catch((e=>{console.error("审核操作失败:",e),H.nk.error("审核操作失败"),d.value=!1}))):H.nk.warning("请填写拒绝原因")},P=()=>{H.nk.success("数据导出功能开发中")};return(0,i.sV)((()=>{k()})),{formatDateTime:G.r6,dateRange:e,list:t,total:a,listLoading:l,listQuery:g,statistics:v,statusOptions:y,typeOptions:h,dialogVisible:s,detailLoading:n,detail:o,auditDialogVisible:r,auditSubmitting:d,auditAction:u,auditForm:m,imagePreviewVisible:c,previewImageUrl:p,handleFilter:f,resetQuery:b,handleDateChange:w,getStatusType:_,getStatusText:S,getTypeText:C,getTypeTagType:A,handleDetail:F,previewImage:x,handleApprove:V,handleReject:T,submitAudit:I,exportData:P}}};var re=a(1241);const de=(0,re.A)(oe,[["render",Y],["__scopeId","data-v-c124649c"]]),ue=de},9190:(e,t,a)=>{a.d(t,{A:()=>d});var i=a(6768),l=a(4232);function s(e,t,a,s,n,o){const r=(0,i.g2)("el-pagination");return(0,i.uX)(),(0,i.CE)("div",{class:(0,l.C4)([{hidden:a.hidden},"pagination-container"])},[(0,i.bF)(r,{background:a.background,"current-page":o.currentPage,"onUpdate:currentPage":t[0]||(t[0]=e=>o.currentPage=e),"page-size":o.pageSize,"onUpdate:pageSize":t[1]||(t[1]=e=>o.pageSize=e),layout:a.layout,"page-sizes":a.pageSizes,total:a.total,onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["background","current-page","page-size","layout","page-sizes","total","onSizeChange","onCurrentChange"])],2)}const n={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:10},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},emits:["update:page","update:limit","pagination"],computed:{currentPage:{get(){return this.page},set(e){this.$emit("update:page",e)}},pageSize:{get(){return this.limit},set(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange(e){this.$emit("pagination",{page:this.currentPage,limit:e})},handleCurrentChange(e){this.$emit("pagination",{page:e,limit:this.pageSize})}}};var o=a(1241);const r=(0,o.A)(n,[["render",s],["__scopeId","data-v-5035dd33"]]),d=r}}]);