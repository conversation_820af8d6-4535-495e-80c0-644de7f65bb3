import request from '@/utils/request'

// 管理员登录
export function adminLogin(data) {
  return request({
    url: '/admin/auth/login',
    method: 'post',
    data
  })
}

// 管理员退出登录
export function adminLogout() {
  return request({
    url: '/admin/auth/logout',
    method: 'post'
  })
}

// 获取管理员信息
export function getAdminInfo() {
  return request({
    url: '/admin/auth/info',
    method: 'get'
  })
}

// 刷新管理员Token
export function refreshAdminToken() {
  return request({
    url: '/admin/auth/refresh-token',
    method: 'post'
  })
}

// 获取管理员面板统计数据
export function getAdminDashboardStats() {
  return request({
    url: '/admin/statistics/dashboard',
    method: 'get'
  })
}

// 获取系统日志列表
export function getSystemLogs(query) {
  return request({
    url: '/admin/system/logs',
    method: 'get',
    params: query
  })
}

// 获取管理员操作记录
export function getAdminOperationLogs(query) {
  return request({
    url: '/admin/system/operation-logs',
    method: 'get',
    params: query
  })
}

// 系统配置相关接口
export function getSystemConfig() {
  return request({
    url: '/admin/system/config',
    method: 'get'
  })
}

export function updateSystemConfig(data) {
  return request({
    url: '/admin/system/config',
    method: 'put',
    data
  })
}

// 后台管理员用户管理
export function getAdminUserList(query) {
  return request({
    url: '/admin/users/admin-list',
    method: 'get',
    params: query
  })
}

export function addAdminUser(data) {
  return request({
    url: '/admin/users/admin',
    method: 'post',
    data
  })
}

export function updateAdminUser(data) {
  return request({
    url: '/admin/users/admin',
    method: 'put',
    data
  })
}

export function deleteAdminUser(id) {
  return request({
    url: `/admin/users/admin/${id}`,
    method: 'delete'
  })
}

export function resetAdminPassword(data) {
  return request({
    url: '/admin/users/reset-password',
    method: 'post',
    data
  })
}

// 获取管理员角色列表
export function getAdminRoleList() {
  return request({
    url: '/admin/roles',
    method: 'get'
  })
} 