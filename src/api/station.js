import request from '@/utils/request'

/**
 * 获取驿站列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getStationList(params) {
  return request({
    url: '/stations/stations',
    method: 'get',
    params
  })
}

/**
 * 获取驿站详情
 * @param {Number} id 驿站ID
 * @returns {Promise}
 */
export function getStationDetail(id) {
  return request({
    url: `/stations/stations/${id}`,
    method: 'get'
  })
}

/**
 * 添加驿站
 * @param {Object} data 驿站数据
 * @returns {Promise}
 */
export function addStation(data) {
  return request({
    url: '/station',
    method: 'post',
    data
  })
}

/**
 * 修改驿站
 * @param {Object} data 驿站数据
 * @returns {Promise}
 */
export function updateStation(data) {
  return request({
    url: '/station',
    method: 'put',
    data
  })
}

/**
 * 删除驿站
 * @param {Number} id 驿站ID
 * @returns {Promise}
 */
export function deleteStation(id) {
  return request({
    url: `/stations/admin/stations/${id}`,
    method: 'delete'
  })
}

/**
 * 审核驿站
 * @param {Object} data 审核数据
 * @returns {Promise}
 */
export function auditStation(data) {
  return request({
    url: '/station/audit',
    method: 'put',
    data
  })
}

/**
 * 更新驿站状态
 * @param {Object} data 状态数据
 * @returns {Promise}
 */
export function updateStationStatus(data) {
  return request({
    url: '/station/status',
    method: 'put',
    data
  })
}

/**
 * 导出驿站
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function exportStation(params) {
  return request({
    url: '/station/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取驿站统计数据
 * @returns {Promise}
 */
export function getStationStatistics() {
  return request({
    url: '/station/statistics',
    method: 'get'
  })
}

/**
 * 测试用删除函数 - 不需要验证
 * @param {Number} id 驿站ID
 * @returns {Promise}
 */
export function testDeleteStation(id) {
  return request({
    url: `/stations/delete-station/${id}`,
    method: 'delete'
  })
}

/**
 * 获取驿站认证申请列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getVerificationList(params) {
  return request({
    url: '/station/verify/list',
    method: 'get',
    params
  })
}

/**
 * 获取驿站认证申请详情
 * @param {Number} id 申请ID
 * @returns {Promise}
 */
export function getVerificationDetail(id) {
  return request({
    url: `/station/verify/${id}`,
    method: 'get'
  })
}

/**
 * 审核驿站认证申请
 * @param {Number} id 申请ID
 * @param {Object} data 审核数据
 * @returns {Promise}
 */
export function reviewVerification(id, data) {
  return request({
    url: `/station/verify/${id}/review`,
    method: 'post',
    data
  })
} 