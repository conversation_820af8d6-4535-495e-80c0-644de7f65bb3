/**
 * 小程序API调用接口
 * 用于在后台管理系统中调用小程序的API
 */
import request from '@/utils/request';
import { miniprogramApiProxy } from './shared/service';

/**
 * 获取小程序用户列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getMiniprogramUsers(params) {
  return request({
    url: '/admin/miniprogram/user/getList',
    method: 'post',
    data: params
  });
}

/**
 * 获取小程序用户详情
 * @param {Number} id 用户ID
 * @returns {Promise}
 */
export function getMiniprogramUserDetail(id) {
  return request({
    url: '/admin/miniprogram/user/getDetail',
    method: 'post',
    data: { id }
  });
}

/**
 * 获取小程序驿站列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getMiniprogramStations(params) {
  return request({
    url: '/admin/miniprogram/station/getList',
    method: 'post',
    data: params
  });
}

/**
 * 获取小程序驿站详情
 * @param {Number} id 驿站ID
 * @returns {Promise}
 */
export function getMiniprogramStationDetail(id) {
  return request({
    url: '/admin/miniprogram/station/getDetail',
    method: 'post',
    data: { id }
  });
}

/**
 * 获取小程序订单列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getMiniprogramOrders(params) {
  return request({
    url: '/admin/miniprogram/order/getList',
    method: 'post',
    data: params
  });
}

/**
 * 获取小程序订单详情
 * @param {Number} id 订单ID
 * @returns {Promise}
 */
export function getMiniprogramOrderDetail(id) {
  return request({
    url: '/admin/miniprogram/order/getDetail',
    method: 'post',
    data: { id }
  });
}

/**
 * 获取小程序认证申请列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getMiniprogramVerifications(params) {
  console.log('[API] 调用小程序认证列表API, 参数:', params);
  return request({
    url: '/admin/miniprogram/verification/getList',
    method: 'post',
    data: params
  }).then(response => {
    console.log('[API] 小程序认证列表API响应成功:', response);
    return response;
  }).catch(error => {
    console.error('[API] 小程序认证列表API请求失败:', error);
    // 捕获错误并增加更多详细信息
    if (error.response) {
      console.error('[API] 响应状态码:', error.response.status);
      console.error('[API] 响应数据:', error.response.data);
      
      // 如果是401错误，记录更多信息
      if (error.response.status === 401) {
        const { getToken } = require('@/utils/auth');
        console.error('[API] 401错误 - 当前Token:', getToken('admin_token') ? '存在' : '不存在');
      }
    }
    throw error;
  });
}

/**
 * 获取小程序认证申请详情
 * @param {Number} id 申请ID
 * @returns {Promise}
 */
export function getMiniprogramVerificationDetail(id) {
  console.log('[API] 调用小程序认证详情API, ID:', id);
  return request({
    url: '/admin/miniprogram/verification/getDetail',
    method: 'post',
    data: { id }
  }).then(response => {
    console.log('[API] 小程序认证详情API响应成功:', response);
    return response;
  }).catch(error => {
    console.error('[API] 小程序认证详情API请求失败:', error);
    // 捕获错误并增加更多详细信息
    if (error.response) {
      console.error('[API] 响应状态码:', error.response.status);
      console.error('[API] 响应数据:', error.response.data);
    }
    throw error;
  });
}

/**
 * 获取小程序用户数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniUsers(params) {
  return miniprogramApiProxy.invoke('user', 'getList', params);
}

/**
 * 获取小程序用户详情
 * @param {String} id - 用户ID
 * @returns {Promise}
 */
export function getMiniUserDetail(id) {
  return miniprogramApiProxy.getData('user', id);
}

/**
 * 获取小程序订单数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniOrders(params) {
  return miniprogramApiProxy.invoke('order', 'getList', params);
}

/**
 * 获取小程序驿站数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniStations(params) {
  return miniprogramApiProxy.invoke('station', 'getList', params);
}

/**
 * 获取小程序驿站详情
 * @param {String} id - 驿站ID
 * @returns {Promise}
 */
export function getMiniStationDetail(id) {
  return miniprogramApiProxy.getData('station', id);
}

/**
 * 获取小程序推广码数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniPromotionCodes(params) {
  return miniprogramApiProxy.invoke('promotion', 'getCodeList', params);
}

/**
 * 获取小程序推广佣金数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniCommissions(params) {
  return miniprogramApiProxy.invoke('promotion', 'getCommissionList', params);
}

/**
 * 获取小程序数据统计
 * @param {String} module - 模块名称
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniStatistics(module, params) {
  return miniprogramApiProxy.invoke('statistics', 'getStats', { module, ...params });
}

/**
 * 发送小程序消息通知
 * @param {Object} data - 消息数据
 * @returns {Promise}
 */
export function sendMiniNotification(data) {
  return miniprogramApiProxy.invoke('notification', 'send', data);
}

/**
 * 同步数据到小程序
 * @param {String} module - 模块名称
 * @param {Object} data - 数据
 * @returns {Promise}
 */
export function syncToMiniprogram(module, data) {
  return request({
    url: `/v1/sync/${module}/push`,
    method: 'post',
    data
  });
}

/**
 * 获取小程序实时日志
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniLogs(params) {
  return miniprogramApiProxy.invoke('system', 'getLogs', params);
}

/**
 * 获取小程序版本信息
 * @returns {Promise}
 */
export function getMiniVersion() {
  return miniprogramApiProxy.invoke('system', 'getVersion');
}

/**
 * 获取小程序用户反馈
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniFeedback(params) {
  return miniprogramApiProxy.invoke('feedback', 'getList', params);
}

/**
 * 回复小程序用户反馈
 * @param {String} id - 反馈ID
 * @param {String} content - 回复内容
 * @returns {Promise}
 */
export function replyMiniFeedback(id, content) {
  return miniprogramApiProxy.invoke('feedback', 'reply', { id, content });
} 