import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout'

// 公共路由
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: { title: '仪表盘', icon: 'Odometer', affix: true }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/user',
    name: 'System',
    meta: { title: '系统管理', icon: 'Setting' },
    children: [
      {
        path: 'user',
        component: () => import('@/views/system/user/index'),
        name: 'User',
        meta: { title: '用户管理', icon: 'User' }
      },
      {
        path: 'role',
        component: () => import('@/views/system/role/index'),
        name: 'Role',
        meta: { title: '角色管理', icon: 'UserFilled' }
      },
      {
        path: 'menu',
        component: () => import('@/views/system/menu/index'),
        name: 'Menu',
        meta: { title: '菜单管理', icon: 'Menu' }
      }
    ]
  },
  {
    path: '/station',
    component: Layout,
    redirect: '/station/list',
    name: 'Station',
    meta: { title: '驿站管理', icon: 'House' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/station/list/index'),
        name: 'StationList',
        meta: { title: '驿站列表', icon: 'List' }
      },
      {
        path: 'verify',
        component: () => import('@/views/station/verify/index'),
        name: 'StationVerify',
        meta: { title: '认证审核', icon: 'Checked' }
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    redirect: '/order/list',
    name: 'Order',
    meta: { title: '订单管理', icon: 'Tickets' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/order/list/index'),
        name: 'OrderList',
        meta: { title: '订单列表', icon: 'List' }
      },
      {
        path: 'refund',
        component: () => import('@/views/order/refund/index'),
        name: 'OrderRefund',
        meta: { title: '退款管理', icon: 'TurnOff' }
      }
    ]
  },
  {
    path: '/service',
    component: Layout,
    redirect: '/service/list',
    name: 'Service',
    meta: { title: '服务管理', icon: 'Service' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/service/list/index'),
        name: 'ServiceList',
        meta: { title: '服务列表', icon: 'List' }
      }
    ]
  },
  {
    path: '/job',
    component: Layout,
    redirect: '/job/list',
    name: 'Job',
    meta: { title: '工作岗位', icon: 'Suitcase' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/job/list/index'),
        name: 'JobList',
        meta: { title: '岗位列表', icon: 'List' }
      }
    ]
  },
  {
    path: '/device',
    component: Layout,
    redirect: '/device/list',
    name: 'Device',
    meta: { title: '设备管理', icon: 'Box' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/device/list/index'),
        name: 'DeviceList',
        meta: { title: '设备列表', icon: 'List' }
      }
    ]
  },
  {
    path: '/content',
    component: Layout,
    redirect: '/content/banner',
    name: 'Content',
    meta: { title: '内容管理', icon: 'Document' },
    children: [
      {
        path: 'audit',
        component: () => import('@/views/content/audit/index'),
        name: 'ContentAudit',
        meta: { title: '统一审核中心', icon: 'Check' }
      },
      {
        path: 'audit/detail/:id/:type',
        component: () => import('@/views/content/audit/detail'),
        name: 'AuditDetail',
        meta: { title: '审核详情', icon: 'View', activeMenu: '/content/audit' },
        hidden: true
      },
      {
        path: 'management',
        component: () => import('@/views/content/management/index'),
        name: 'ContentManagement',
        meta: { title: '内容上下架管理', icon: 'SwitchButton' }
      },
      {
        path: 'quality',
        component: () => import('@/views/content/quality/index'),
        name: 'ContentQuality',
        meta: { title: '内容质量管理', icon: 'Star' }
      },
      {
        path: 'banner',
        component: () => import('@/views/content/banner/index'),
        name: 'Banner',
        meta: { title: '轮播图管理', icon: 'Picture' }
      },
      {
        path: 'notice',
        component: () => import('@/views/content/notice/index'),
        name: 'Notice',
        meta: { title: '公告管理', icon: 'Bell' }
      }
    ]
  },
  {
    path: '/statistics',
    component: Layout,
    redirect: '/statistics/overview',
    name: 'Statistics',
    meta: { title: '统计分析', icon: 'DataAnalysis' },
    children: [
      {
        path: 'overview',
        component: () => import('@/views/statistics/overview/index'),
        name: 'Overview',
        meta: { title: '业务概览', icon: 'DataLine' }
      },
      {
        path: 'user',
        component: () => import('@/views/statistics/user/index'),
        name: 'UserStatistics',
        meta: { title: '用户分析', icon: 'User' }
      },
      {
        path: 'order',
        component: () => import('@/views/statistics/order/index'),
        name: 'OrderStatistics',
        meta: { title: '订单分析', icon: 'Tickets' }
      }
    ]
  },
  {
    path: '/profile',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '',
        component: () => import('@/views/profile/index/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'User' }
      }
    ]
  },
  {
    path: '/transfer',
    component: Layout,
    redirect: '/transfer/list',
    name: 'Transfer',
    meta: { title: '驿站转让', icon: 'Shop' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/transfer/list/index'),
        name: 'TransferList',
        meta: { title: '转让列表', icon: 'List' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/transfer/detail/index'),
        name: 'TransferDetail',
        meta: { title: '转让详情', icon: 'View', activeMenu: '/transfer/list' },
        hidden: true
      },
      {
        path: 'statistics',
        component: () => import('@/views/transfer/statistics/index'),
        name: 'TransferStatistics',
        meta: { title: '转让统计', icon: 'PieChart' }
      }
    ]
  },
  {
    path: '/equipment',
    component: Layout,
    redirect: '/equipment/list',
    name: 'Equipment',
    meta: { title: '设备交易', icon: 'Printer' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/equipment/list/index'),
        name: 'EquipmentList',
        meta: { title: '设备列表', icon: 'List' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/equipment/detail/index'),
        name: 'EquipmentDetail',
        meta: { title: '设备详情', icon: 'View', activeMenu: '/equipment/list' },
        hidden: true
      },
      {
        path: 'category',
        component: () => import('@/views/equipment/category/index'),
        name: 'EquipmentCategory',
        meta: { title: '设备分类', icon: 'Folder' }
      },
      {
        path: 'price',
        component: () => import('@/views/equipment/price/index'),
        name: 'EquipmentPrice',
        meta: { title: '价格参考', icon: 'Money' }
      }
    ]
  },
  {
    path: '/recruitment',
    component: Layout,
    redirect: '/recruitment/list',
    name: 'Recruitment',
    meta: { title: '招聘求职', icon: 'Briefcase' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/recruitment/list/index'),
        name: 'RecruitmentList',
        meta: { title: '招聘列表', icon: 'List' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/recruitment/detail/index'),
        name: 'RecruitmentDetail',
        meta: { title: '招聘详情', icon: 'View', activeMenu: '/recruitment/list' },
        hidden: true
      },
      {
        path: 'category',
        component: () => import('@/views/recruitment/category/index'),
        name: 'JobCategory',
        meta: { title: '职位分类', icon: 'Folder' }
      },
      {
        path: 'statistics',
        component: () => import('@/views/recruitment/statistics/index'),
        name: 'RecruitmentStatistics',
        meta: { title: '招聘统计', icon: 'PieChart' }
      }
    ]
  },
  {
    path: '/substitution',
    component: Layout,
    redirect: '/substitution/list',
    name: 'Substitution',
    meta: { title: '顶班服务', icon: 'Timer' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/substitution/list/index'),
        name: 'SubstitutionList',
        meta: { title: '顶班列表', icon: 'List' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/substitution/detail/index'),
        name: 'SubstitutionDetail',
        meta: { title: '顶班详情', icon: 'View', activeMenu: '/substitution/list' },
        hidden: true
      },
      {
        path: 'application',
        component: () => import('@/views/substitution/application/index'),
        name: 'SubstitutionApplication',
        meta: { title: '接单申请', icon: 'Finished' }
      },
      {
        path: 'evaluation',
        component: () => import('@/views/substitution/evaluation/index'),
        name: 'SubstitutionEvaluation',
        meta: { title: '服务评价', icon: 'Star' }
      }
    ]
  },
  {
    path: '/shop',
    component: Layout,
    redirect: '/shop/decoration',
    name: 'Shop',
    meta: { title: '商城管理', icon: 'ShoppingBag' },
    children: [
      {
        path: 'decoration',
        component: () => import('@/views/shop/decoration/index'),
        name: 'ShopDecoration',
        meta: { title: '小程序装修', icon: 'Brush' }
      }
    ]
  },
  {
    path: '/promotion',
    component: Layout,
    redirect: '/promotion/user',
    name: 'Promotion',
    meta: { title: '推广赚钱', icon: 'Present' },
    children: [
      {
        path: 'user',
        component: () => import('@/views/promotion/user/index'),
        name: 'PromoterUser',
        meta: { title: '推广员管理', icon: 'User' }
      },
      {
        path: 'code',
        component: () => import('@/views/promotion/code/index'),
        name: 'PromoCode',
        meta: { title: '推广码管理', icon: 'DocumentCopy' }
      },
      {
        path: 'commission',
        component: () => import('@/views/promotion/commission/index'),
        name: 'Commission',
        meta: { title: '佣金规则', icon: 'SetUp' }
      },
      {
        path: 'withdrawal',
        component: () => import('@/views/promotion/withdrawal/index'),
        name: 'Withdrawal',
        meta: { title: '提现管理', icon: 'Money' }
      },
      {
        path: 'statistics',
        component: () => import('@/views/promotion/statistics/index'),
        name: 'PromotionStatistics',
        meta: { title: '推广统计', icon: 'PieChart' }
      }
    ]
  },
  {
    path: '/identity',
    component: Layout,
    redirect: '/identity/list',
    name: 'Identity',
    meta: { title: '实名认证管理', icon: 'UserFilled' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/identity/list/index'),
        name: 'IdentityList',
        meta: { title: '认证列表', icon: 'List' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/identity/detail/index'),
        name: 'IdentityDetail',
        meta: { title: '认证详情', icon: 'View', activeMenu: '/identity/list' },
        hidden: true
      },
      {
        path: 'statistics',
        component: () => import('@/views/identity/statistics/index'),
        name: 'IdentityStatistics',
        meta: { title: '认证统计', icon: 'DataAnalysis' }
      }
    ]
  },
  // 404页面必须放在最后
  { path: '/:pathMatch(.*)*', redirect: '/404', hidden: true }
]

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior: () => ({ top: 0 })
})

export default router 