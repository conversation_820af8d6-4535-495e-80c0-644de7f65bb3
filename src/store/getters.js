const getters = {
  sidebar: state => state.app.sidebar,
  size: state => state.app.size,
  device: state => state.app.device,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  introduction: state => state.user.introduction,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  routes: state => state.permission.routes,
  
  // 管理员相关的getters
  adminToken: state => state.admin.token,
  adminAvatar: state => state.admin.avatar,
  adminName: state => state.admin.name,
  adminRoles: state => state.admin.roles,
  adminPermissions: state => state.admin.permissions,
  isAdmin: state => {
    // 检查是否具有管理员token和管理员角色
    return state.admin.token && 
      state.admin.roles && 
      (state.admin.roles.includes('admin') || state.admin.roles.includes('superadmin'));
  }
}

export default getters 