import { login, logout, getInfo } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'

const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: []
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_INTRODUCTION: (state, introduction) => {
      state.introduction = introduction
    }
  },

  actions: {
    // 登录
    login({ commit }, userInfo) {
      const { username, password } = userInfo
      return new Promise((resolve, reject) => {
        console.log('[User Store] 开始登录, 用户名:', username)
        login({ username: username.trim(), password: password }).then(response => {
          console.log('[User Store] 登录成功, 服务端返回:', response)
          
          const { data } = response
          
          if (!data || !data.token) {
            console.error('[User Store] 登录返回数据中缺少token')
            reject(new Error('登录失败: 服务器未返回有效token'))
            return
          }
          
          // 设置token - 对于管理员使用admin_token
          const tokenType = data.roles && data.roles.includes('admin') ? 'admin_token' : 'user_token'
          console.log(`[User Store] 设置${tokenType}, token值:`, data.token.substring(0, 15) + '...')
          
          // 保存token到cookie
          commit('SET_TOKEN', data.token)
          setToken(data.token, tokenType)
          
          // 登录成功后立即测试token可用性
          setTimeout(() => {
            console.log('[User Store] 登录后token状态检查:')
            console.log('- Admin Token:', getToken('admin_token') ? '已设置' : '未设置')
            console.log('- User Token:', getToken('user_token') ? '已设置' : '未设置')
          }, 500)
          
          resolve(data)
        }).catch(error => {
          console.error('[User Store] 登录失败:', error)
          reject(error)
        })
      })
    },

    // 获取用户信息
    getInfo({ commit, state }) {
      console.log('====== 获取用户信息 ======');
      console.log('当前token:', state.token);
      
      return new Promise((resolve, reject) => {
        getInfo()
          .then(response => {
            const { data } = response
            console.log('获取用户信息成功:', data);
            
            if (!data) {
              reject('获取用户信息失败，请重新登录')
              return
            }

            const { roles, name, avatar } = data

            // 角色必须是非空数组
            if (!roles || roles.length <= 0) {
              console.log('获取用户角色失败，角色信息:', roles);
              reject('角色信息不能为空')
              return
            }
            
            console.log('设置用户角色:', roles);
            console.log('设置用户信息:', data);
            
            commit('SET_ROLES', roles)
            commit('SET_NAME', name)
            commit('SET_AVATAR', avatar)
            commit('SET_INTRODUCTION', data.introduction)
            resolve(data)
          })
          .catch(error => {
            console.error('获取用户信息失败:', error);
            reject(error)
          })
      })
    },
    
    // 退出系统
    logout({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout().then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端登出
    fedLogout({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user 