import { adminLogin, adminLogout, getAdminInfo } from '@/api/admin'
import { getToken, setToken, removeToken } from '@/utils/auth'

const admin = {
  namespaced: true,
  
  state: {
    token: getToken('admin_token'),
    name: '',
    avatar: '',
    roles: [],
    permissions: []
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    }
  },

  actions: {
    // 管理员登录
    login({ commit }, userInfo) {
      const { username, password } = userInfo
      return new Promise((resolve, reject) => {
        adminLogin({ username, password }).then(response => {
          if (response.code === 0 && response.data && response.data.token) {
            console.log('管理员登录成功:', response.data);
            const token = response.data.token
            
            // 使用独立的管理员token存储
            setToken(token, 'admin_token')
            commit('SET_TOKEN', token)
            
            // 如果返回了管理员信息，直接设置
            if (response.data.admin) {
              const admin = response.data.admin
              commit('SET_NAME', admin.nickname || admin.phone || '')
              commit('SET_AVATAR', admin.avatar || '')
              
              // 设置角色和权限
              if (admin.roles && admin.roles.length > 0) {
                commit('SET_ROLES', admin.roles)
              } else {
                commit('SET_ROLES', ['admin'])
              }
              
              if (admin.permissions) {
                commit('SET_PERMISSIONS', admin.permissions)
              }
            }
            
            resolve(response)
          } else {
            reject(new Error(response.message || '管理员登录失败'))
          }
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取管理员信息
    getInfo({ commit, state }) {
      console.log('====== 获取管理员信息 ======');
      console.log('当前管理员token:', state.token);
      
      return new Promise((resolve, reject) => {
        getAdminInfo()
          .then(response => {
            const { data } = response
            console.log('获取管理员信息成功:', data);
            
            if (!data) {
              reject('获取管理员信息失败，请重新登录')
              return
            }

            const { roles, nickname, avatar, permissions } = data

            // 角色必须是非空数组
            if (!roles || roles.length <= 0) {
              console.log('获取管理员角色失败:', roles);
              reject('管理员角色信息不能为空')
              return
            }
            
            console.log('设置管理员角色:', roles);
            console.log('设置管理员信息:', data);
            
            commit('SET_ROLES', roles)
            commit('SET_NAME', nickname || data.phone || '')
            commit('SET_AVATAR', avatar || '')
            
            if (permissions) {
              commit('SET_PERMISSIONS', permissions)
            }
            
            resolve(data)
          })
          .catch(error => {
            console.error('获取管理员信息失败:', error);
            reject(error)
          })
      })
    },
    
    // 管理员退出系统
    logout({ commit, state }) {
      return new Promise((resolve, reject) => {
        adminLogout().then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken('admin_token')
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端管理员登出
    fedLogout({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_PERMISSIONS', [])
        removeToken('admin_token')
        resolve()
      })
    },
    
    // 重置管理员Token
    resetToken({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_PERMISSIONS', [])
        removeToken('admin_token')
        resolve()
      })
    }
  }
}

export default admin 