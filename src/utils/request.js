import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  timeout: 10000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    console.log('[REQUEST] 发送请求:', config.url)
    
    // 显示当前token状态
    const token = getToken('admin_token')
    console.log(`[REQUEST] 当前Token状态: ${token ? '已设置' : '未设置'}`)
    if (token) {
      console.log(`[REQUEST] Token值: ${token.substring(0, 15)}...`)
    }
    
    // 如果有token，添加到请求头
    if (store.getters.token) {
      console.log('[REQUEST] 在请求头中添加token')
      // 设置请求头携带token
      config.headers['Authorization'] = `Bearer ${store.getters.token}`
    }
    
    // 检查请求URL是否包含admin，确保正确设置token
    if (config.url.includes('/admin/')) {
      console.log('[REQUEST] 检测到管理员API请求')
      const adminToken = getToken('admin_token')
      if (adminToken) {
        console.log('[REQUEST] 使用管理员Token')
        config.headers['Authorization'] = `Bearer ${adminToken}`
      } else {
        console.warn('[REQUEST] 警告: 管理员API请求但未找到管理员Token')
      }
    }
    
    // 注意：config.url 可能已经包含 /api 前缀，所以我们不再自动添加
    // 记录最终请求URL和头信息
    console.log('[REQUEST] 最终请求URL:', config.url)
    console.log('[REQUEST] 请求头Authorization:', config.headers['Authorization'] || '未设置')
    
    return config
  },
  error => {
    console.error('[REQUEST] 请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    console.log('[RESPONSE] 收到响应:', response.config.url, response.status)
    
    const res = response.data
    
    // 如果响应状态码不是200，认为请求有错误
    if (response.status !== 200) {
      console.error('[RESPONSE] 响应状态码错误:', response.status)
      Message({
        message: res.message || '服务器响应错误',
        type: 'error',
        duration: 5 * 1000
      })
      return Promise.reject(new Error(res.message || '服务器响应错误'))
    }
    
    // 检查业务状态码
    if (res.code !== 0 && res.code !== 200) {
      console.error('[RESPONSE] 业务状态码错误:', res.code, res.message)
      
      // 特殊处理401错误（未授权）
      if (res.code === 401) {
        console.error('[RESPONSE] 检测到401错误 - Token可能无效或已过期')
        
        // 检查当前Token状态
        const token = getToken('admin_token')
        console.log(`[RESPONSE] 401错误时的Token状态: ${token ? '存在但可能无效' : '不存在'}`)
        
        // 弹出确认对话框，询问是否重新登录
        MessageBox.confirm(
          '您的登录状态已失效，请重新登录',
          '登录超时',
          {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          // 登出并重定向到登录页
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      } else {
        // 其他业务错误
        Message({
          message: res.message || '操作失败',
          type: 'error',
          duration: 5 * 1000
        })
      }
      
      return Promise.reject(new Error(res.message || '未知错误'))
    }
    
    // 正常返回数据
    return res
  },
  error => {
    console.error('[RESPONSE] 请求失败:', error)
    
    // 检查是否是401错误
    if (error.response && error.response.status === 401) {
      console.error('[RESPONSE] 捕获到401未授权错误')
      
      // 检查当前Token状态
      const token = getToken('admin_token')
      console.log(`[RESPONSE] 401错误时的Token状态: ${token ? '存在但可能无效' : '不存在'}`)
      
      // 弹出确认对话框
      MessageBox.confirm(
        '您的登录状态已失效，请重新登录',
        '登录超时',
        {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 登出并重定向到登录页
        store.dispatch('user/logout').then(() => {
          location.reload()
        })
      })
    } else {
      // 其他错误
      Message({
        message: error.message || '请求失败，请稍后重试',
        type: 'error',
        duration: 5 * 1000
      })
    }
    
    return Promise.reject(error)
  }
)

export default service 