import Cookies from 'js-cookie'

// Tokens
const AdminTokenKey = 'Admin-Token'
const UserTokenKey = 'User-Token'

// Default expiration time in days
const defaultExpires = 7

/**
 * Get token by type
 * @param {string} type - Token type (admin_token or user_token)
 * @returns {string} token value
 */
export function getToken(type = 'admin_token') {
  console.log(`[AUTH] Getting token of type: ${type}`);
  const key = type === 'admin_token' ? AdminTokenKey : UserTokenKey;
  const token = Cookies.get(key);
  console.log(`[AUTH] Retrieved token: ${token ? token.substring(0, 15) + '...' : 'null'}`);
  return token;
}

/**
 * Set token by type
 * @param {string} token - Token value
 * @param {string} type - Token type (admin_token or user_token)
 * @param {number} expires - Expiration time in days
 */
export function setToken(token, type = 'admin_token', expires = defaultExpires) {
  console.log(`[AUTH] Setting ${type} token: ${token ? token.substring(0, 15) + '...' : 'null'}`);
  const key = type === 'admin_token' ? AdminTokenKey : UserTokenKey;
  
  if (token) {
    Cookies.set(key, token, { expires });
    console.log(`[AUTH] Token set successfully with expiry of ${expires} days`);
  } else {
    console.warn('[AUTH] Attempted to set null or empty token');
  }
}

/**
 * Remove token by type
 * @param {string} type - Token type (admin_token or user_token)
 */
export function removeToken(type = 'admin_token') {
  console.log(`[AUTH] Removing token of type: ${type}`);
  const key = type === 'admin_token' ? AdminTokenKey : UserTokenKey;
  Cookies.remove(key);
}

/**
 * Check if token exists and is valid
 * @param {string} type - Token type (admin_token or user_token)
 * @returns {boolean} true if token exists
 */
export function hasToken(type = 'admin_token') {
  const token = getToken(type);
  const hasValidToken = !!token;
  console.log(`[AUTH] Token check for ${type}: ${hasValidToken ? 'Valid' : 'Invalid or missing'}`);
  return hasValidToken;
}

/**
 * Get all tokens
 * @returns {Object} Object containing all tokens
 */
export function getAllTokens() {
  const adminToken = getToken('admin_token');
  const userToken = getToken('user_token');
  
  console.log('[AUTH] Retrieved all tokens:');
  console.log(`- Admin token: ${adminToken ? 'Present' : 'Missing'}`);
  console.log(`- User token: ${userToken ? 'Present' : 'Missing'}`);
  
  return {
    adminToken,
    userToken
  };
}

// 检查是否已登录管理员
export function isAdminLoggedIn() {
  return !!getToken('admin_token')
}

// 检查是否已登录普通用户
export function isUserLoggedIn() {
  return !!getToken('user_token')
}

// 获取当前登录类型
export function getLoginType() {
  if (isAdminLoggedIn()) return 'admin'
  if (isUserLoggedIn()) return 'user'
  return null
}

// 检查token是否有效
export function isTokenValid() {
  const token = getToken()
  return !!token // 简单检查token是否存在
}

// 刷新token（如果需要，可以添加请求后端刷新token的逻辑）
export function refreshToken() {
  console.log('刷新token')
  // 这里可以添加刷新token的逻辑
} 