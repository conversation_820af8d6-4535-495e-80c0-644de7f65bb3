<template>
  <div class="login-container">
    <el-form ref="loginForm" :model="loginFormData" :rules="loginRules" class="login-form" autocomplete="on" label-position="left">
      <div class="title-container">
        <h3 class="title">{{ isAdminLogin ? '管理员登录' : '用户登录' }}</h3>
        <el-button size="mini" type="text" @click="switchLoginType">
          {{ isAdminLogin ? '切换到用户登录' : '切换到管理员登录' }}
        </el-button>
      </div>

      <!-- 用户名输入框 -->
      <el-form-item prop="username">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input
          ref="username"
          v-model="loginFormData.username"
          :placeholder="isAdminLogin ? '管理员账号' : '手机号'"
          name="username"
          type="text"
          tabindex="1"
          autocomplete="on"
        />
      </el-form-item>

      <!-- 密码输入框 -->
      <el-form-item prop="password">
        <span class="svg-container">
          <svg-icon icon-class="password" />
        </span>
        <el-input
          :key="passwordType"
          ref="password"
          v-model="loginFormData.password"
          :type="passwordType"
          placeholder="密码"
          name="password"
          tabindex="2"
          autocomplete="on"
          @keyup.enter.native="handleLogin"
        />
        <span class="show-pwd" @click="showPwd">
          <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
        </span>
      </el-form-item>

      <!-- 记住我复选框 -->
      <div style="margin-bottom: 20px">
        <el-checkbox v-model="loginFormData.rememberMe">记住我</el-checkbox>
      </div>

      <!-- 登录按钮 -->
      <el-button :loading="loading" type="primary" style="width:100%; margin-bottom:30px;" @click.native.prevent="handleLogin">
        登录
      </el-button>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, watch, nextTick, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { login } from '@/api/user'
import { adminLogin } from '@/api/admin'
import { getToken, setToken } from '@/utils/auth'

export default {
  name: 'Login',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    
    // 表单引用
    const loginForm = ref(null)
    const username = ref(null)
    const password = ref(null)
    
    const loginFormData = reactive({
      username: '',
      password: '',
      rememberMe: false
    })
    
    const loading = ref(false)
    const passwordType = ref('password')
    const isAdminLogin = ref(false)
    const redirect = ref(undefined)
    const otherQuery = ref({})
    
    const loginRules = {
      username: [{ required: true, trigger: 'blur', message: '请输入用户名/手机号' }],
      password: [{ required: true, trigger: 'blur', message: '请输入密码' }]
    }
    
    const showPwd = () => {
      if (passwordType.value === 'password') {
        passwordType.value = ''
      } else {
        passwordType.value = 'password'
      }
      nextTick(() => {
        password.value.focus()
      })
    }
    
    const switchLoginType = () => {
      isAdminLogin.value = !isAdminLogin.value
      loginFormData.username = ''
      loginFormData.password = ''
      
      const query = { ...otherQuery.value }
      if (isAdminLogin.value) {
        query.admin = 'true'
      } else {
        delete query.admin
      }
      
      router.replace({
        path: '/login',
        query
      })
    }
    
    const handleLogin = () => {
      loginForm.value.validate(valid => {
        if (valid) {
          loading.value = true
          console.log('[Login] 尝试登录:', isAdminLogin.value ? '管理员登录' : '用户登录')
          console.log('[Login] 登录数据:', {
            username: loginFormData.username,
            password: '******'
          })
          
          const loginData = {
            username: loginFormData.username.trim(),
            password: loginFormData.password
          }
          
          // 选择正确的登录API
          const loginApi = isAdminLogin.value ? adminLogin : login
          
          loginApi(loginData).then(response => {
            console.log('[Login] 登录成功，响应:', response)
            
            if (!response.data || !response.data.token) {
              console.error('[Login] 登录响应中没有token')
              ElMessage.error('登录失败：服务器响应异常')
              loading.value = false
              return
            }
            
            const { token } = response.data
            
            // 设置token并保存到store
            console.log('[Login] 设置token到store')
            store.commit('SET_TOKEN', token)
            
            // 保存token到cookie，针对管理员或普通用户设置不同的token类型
            const tokenType = isAdminLogin.value ? 'admin_token' : 'user_token'
            console.log(`[Login] 保存${tokenType}到cookie`)
            setToken(token, tokenType)
            
            ElMessage.success('登录成功')
            
            // 登录成功后导航到目标页面
            const targetPath = isAdminLogin.value ? 
              (redirect.value || '/admin/dashboard') : 
              (redirect.value || '/')
            
            console.log('[Login] 导航到:', targetPath)
            
            // 打印token状态以便调试
            console.log('[Login] 登录后Token状态:', getToken(tokenType) ? '已设置' : '未设置')
            
            // 如果是管理员登录，添加延时并刷新页面确保token生效
            if (isAdminLogin.value) {
              setTimeout(() => {
                console.log('[Login] 管理员登录完成，刷新页面应用token')
                router.push({ path: targetPath, query: otherQuery.value })
                setTimeout(() => {
                  window.location.reload()
                }, 100)
              }, 500)
            } else {
              router.push({ path: targetPath, query: otherQuery.value })
            }
            
            loading.value = false
          }).catch(error => {
            console.error('[Login] 登录失败:', error)
            ElMessage.error(error.message || '登录失败，请检查用户名和密码')
            loading.value = false
          })
        } else {
          console.log('[Login] 表单验证失败')
          return false
        }
      })
    }
    
    // 监听路由变化
    watch(() => route.query, query => {
      if (query) {
        redirect.value = query.redirect
        otherQuery.value = getOtherQuery(query)
        
        if (query.admin === 'true') {
          isAdminLogin.value = true
        }
      }
    }, { immediate: true })
    
    // 页面加载时检查token
    onMounted(() => {
      if (getToken('user_token') && !isAdminLogin.value) {
        router.push({ path: redirect.value || '/' })
      }
      
      if (getToken('admin_token') && isAdminLogin.value) {
        router.push({ path: redirect.value || '/admin/dashboard' })
      }
      
      // 自动聚焦用户名输入框
      if (username.value) {
        username.value.focus()
      }
    })
    
    const getOtherQuery = (query) => {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }
    
    return {
      loginForm,
      username,
      password,
      loginFormData,
      loading,
      passwordType,
      isAdminLogin,
      redirect,
      otherQuery,
      loginRules,
      showPwd,
      switchLoginType,
      handleLogin,
      getOtherQuery
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100%;
  width: 100%;
  background-color: #2d3a4b;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .login-form {
    position: relative;
    width: 420px;
    max-width: 100%;
    padding: 160px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }
  
  .title-container {
    position: relative;
    text-align: center;
    
    .title {
      font-size: 26px;
      color: #eee;
      margin: 0px auto 40px auto;
      font-weight: bold;
    }
  }
  
  .svg-container {
    padding: 6px 5px 6px 15px;
    color: #889aa4;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }
  
  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: #889aa4;
    cursor: pointer;
    user-select: none;
  }
  
  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
  
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;
    
    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: #eee;
      height: 47px;
      caret-color: #fff;
    }
  }
}
</style> 