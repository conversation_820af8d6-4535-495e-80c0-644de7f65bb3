<template>
  <div class="app-container">
    <el-card class="filter-container">
      <div class="filter-item">
        <el-input v-model="listQuery.keyword" placeholder="驿站名称/联系人" clearable @keyup.enter="handleFilter" style="width: 200px;" />
        <el-select v-model="listQuery.status" placeholder="审核状态" clearable style="width: 130px" @change="handleFilter">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="listQuery.type" placeholder="驿站类型" clearable style="width: 130px" @change="handleFilter">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          style="width: 260px;"
          @change="handleDateChange"
        />
        <el-button type="primary" icon="Search" @click="handleFilter">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
    </el-card>

    <el-card>
      <div class="table-header">
        <div class="statistics-cards">
          <div class="stat-card primary">
            <div class="stat-value">{{ statistics.total || 0 }}</div>
            <div class="stat-label">总申请数</div>
          </div>
          <div class="stat-card warning">
            <div class="stat-value">{{ statistics.pending || 0 }}</div>
            <div class="stat-label">待审核</div>
          </div>
          <div class="stat-card success">
            <div class="stat-value">{{ statistics.approved || 0 }}</div>
            <div class="stat-label">已通过</div>
          </div>
          <div class="stat-card danger">
            <div class="stat-value">{{ statistics.rejected || 0 }}</div>
            <div class="stat-label">已拒绝</div>
          </div>
        </div>
        <div>
          <el-button type="success" size="small" @click="exportData">导出数据</el-button>
        </div>
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column label="ID" prop="id" align="center" width="80" />
        <el-table-column label="驿站信息" min-width="200">
          <template #default="{row}">
            <div class="station-info">
              <el-image v-if="row.storefront" :src="row.storefront" style="width: 60px; height: 60px; border-radius: 4px;"></el-image>
              <div v-else class="no-image" style="width: 60px; height: 60px; border-radius: 4px; background-color: #f0f2f5; display: flex; align-items: center; justify-content: center;">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="station-detail">
                <div class="station-name">{{ row.stationName }}</div>
                <div class="station-address">{{ row.address }}</div>
                <div>
                  <el-tag size="small" :type="getTypeTagType(row.stationType)">{{ getTypeText(row.stationType) }}</el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="联系人" align="center" width="150">
          <template #default="{row}">
            <div>{{ row.contactName }}</div>
            <div class="contact-phone">{{ row.contactPhone }}</div>
          </template>
        </el-table-column>
        <el-table-column label="营业执照" align="center" width="120">
          <template #default="{row}">
            <el-button v-if="row.businessLicense" size="small" type="primary" plain @click="previewImage(row.businessLicense)">查看</el-button>
            <span v-else>无</span>
          </template>
        </el-table-column>
        <el-table-column label="申请人" align="center" width="150">
          <template #default="{row}">
            <div class="user-info">
              <el-avatar v-if="row.user && row.user.avatar" :src="row.user.avatar" :size="30"></el-avatar>
              <el-avatar v-else :size="30" icon="User"></el-avatar>
              <div class="user-detail">
                <div>{{ row.user ? row.user.nickname || '未设置昵称' : '未知用户' }}</div>
                <div>{{ row.user ? row.user.phone : '' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请时间" width="150" align="center">
          <template #default="{row}">
            <span>{{ formatDateTime(row.createdAt) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="100">
          <template #default="{row}">
            <el-tag :type="getStatusType(row.status)">
              {{ row.statusText || getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="审核时间" width="150" align="center">
          <template #default="{row}">
            <span v-if="row.reviewedAt">{{ formatDateTime(row.reviewedAt) }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template #default="{row}">
            <el-button v-if="row.status === 'pending'" type="success" size="small" @click="handleApprove(row)">通过</el-button>
            <el-button v-if="row.status === 'pending'" type="danger" size="small" @click="handleReject(row)">拒绝</el-button>
            <el-button type="info" size="small" @click="handleDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </el-card>

    <!-- 驿站详情对话框 -->
    <el-dialog title="驿站认证详情" v-model="dialogVisible" width="700px">
      <div v-loading="detailLoading" class="station-detail-dialog">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请编号" :span="2">{{ detail.id }}</el-descriptions-item>
          <el-descriptions-item label="驿站名称" :span="2">{{ detail.stationName }}</el-descriptions-item>
          <el-descriptions-item label="驿站类型">{{ getTypeText(detail.stationType) }}</el-descriptions-item>
          <el-descriptions-item label="品牌">{{ detail.brand || '--' }}</el-descriptions-item>
          <el-descriptions-item label="联系人">{{ detail.contactName }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ detail.contactPhone }}</el-descriptions-item>
          <el-descriptions-item label="详细地址" :span="2">{{ detail.address }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime(detail.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="审核时间">{{ detail.reviewedAt ? formatDateTime(detail.reviewedAt) : '--' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="detail.status !== undefined" :type="getStatusType(detail.status)">
              {{ detail.statusText || getStatusText(detail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="审核备注">{{ detail.remark || '--' }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="image-container">
          <div class="image-section">
            <div class="image-title">门头照</div>
            <div class="image-preview">
              <el-image
                v-if="detail.storefront"
                :src="detail.storefront"
                fit="cover"
                :preview-src-list="[detail.storefront]"
                style="width: 200px; height: 150px; border-radius: 4px;"
              ></el-image>
              <div v-else class="no-image">未上传照片</div>
            </div>
          </div>
          
          <div class="image-section">
            <div class="image-title">内部照</div>
            <div class="image-preview">
              <el-image
                v-if="detail.interior"
                :src="detail.interior"
                fit="cover"
                :preview-src-list="[detail.interior]"
                style="width: 200px; height: 150px; border-radius: 4px;"
              ></el-image>
              <div v-else class="no-image">未上传照片</div>
            </div>
          </div>
        </div>

        <div class="image-container">
          <div class="image-section">
            <div class="image-title">营业执照</div>
            <div class="image-preview">
              <el-image
                v-if="detail.businessLicense"
                :src="detail.businessLicense"
                fit="cover"
                :preview-src-list="[detail.businessLicense]"
                style="width: 200px; height: 150px; border-radius: 4px;"
              ></el-image>
              <div v-else class="no-image">未上传执照</div>
            </div>
          </div>
          
          <div class="image-section">
            <div class="image-title">授权书</div>
            <div class="image-preview">
              <el-image
                v-if="detail.authorization"
                :src="detail.authorization"
                fit="cover"
                :preview-src-list="[detail.authorization]"
                style="width: 200px; height: 150px; border-radius: 4px;"
              ></el-image>
              <div v-else class="no-image">未上传授权书</div>
            </div>
          </div>
        </div>
        
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <template v-if="detail.status === 'pending'">
            <el-button type="success" @click="handleApprove(detail)">通过</el-button>
            <el-button type="danger" @click="handleReject(detail)">拒绝</el-button>
          </template>
        </div>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog :title="auditAction === 'approve' ? '通过驿站认证' : '拒绝驿站认证'" v-model="auditDialogVisible" width="500px">
      <el-form :model="auditForm" label-width="80px">
        <el-form-item label="审核意见">
          <el-input
            v-model="auditForm.remark"
            type="textarea"
            :rows="4"
            :placeholder="auditAction === 'approve' ? '审核通过说明（选填）' : '拒绝原因（必填）'"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取 消</el-button>
          <el-button :type="auditAction === 'approve' ? 'success' : 'danger'" @click="submitAudit" :loading="auditSubmitting">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" v-model="imagePreviewVisible" width="700px" center>
      <div class="image-preview-container">
        <el-image :src="previewImageUrl" style="max-width: 100%; max-height: 500px;"></el-image>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination'
import { formatDateTime } from '@/utils/index'
import { getVerificationList, getVerificationDetail, reviewVerification } from '@/api/station'
import { getMiniprogramVerifications, getMiniprogramVerificationDetail } from '@/api/miniprogram'
import { Picture } from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'

export default {
  name: 'StationVerify',
  components: { Pagination },
  setup() {
    const dateRange = ref([])
    const list = ref([])
    const total = ref(0)
    const listLoading = ref(false)
    const dialogVisible = ref(false)
    const detailLoading = ref(false)
    const detail = ref({})
    const auditDialogVisible = ref(false)
    const auditSubmitting = ref(false)
    const auditAction = ref('approve')
    const imagePreviewVisible = ref(false)
    const previewImageUrl = ref('')
    
    const listQuery = reactive({
      page: 1,
      limit: 10,
      status: '',
      type: '',
      keyword: '',
      start_date: '',
      end_date: ''
    })
    
    const statistics = reactive({
      total: 0,
      pending: 0,
      approved: 0,
      rejected: 0
    })
    
    const auditForm = reactive({
      id: null,
      status: '',
      remark: ''
    })
    
    const statusOptions = [
      { label: '待审核', value: 'pending' },
      { label: '已通过', value: 'approved' },
      { label: '已拒绝', value: 'rejected' }
    ]
    
    const typeOptions = [
      { label: '社区驿站', value: 'community' },
      { label: '快递驿站', value: 'express' },
      { label: '校园驿站', value: 'campus' },
      { label: '写字楼驿站', value: 'office' },
      { label: '商业驿站', value: 'commercial' }
    ]
    
    const getList = async () => {
      try {
        listLoading.value = true
        
        console.log('[Station Verify] 开始获取认证申请列表')
        console.log('[Station Verify] 请求参数:', {
          page: listQuery.page,
          limit: listQuery.limit,
          status: listQuery.status || undefined,
          keyword: listQuery.keyword || undefined
        })
        
        // 使用获取token方法检查当前token
        const adminToken = getToken('admin_token')
        console.log('[Station Verify] 当前Token状态:', adminToken ? '已设置' : '未设置')
        
        try {
          // 首先尝试使用常规API
          const response = await getVerificationList({
            page: listQuery.page,
            limit: listQuery.limit,
            status: listQuery.status || undefined,
            keyword: listQuery.keyword || undefined
          })
          
          console.log('[Station Verify] 获取成功 - 常规API:', response)
          
          list.value = response.data.list
          total.value = response.data.total
          
          // 设置统计数据
          if (response.data.statusCount) {
            statistics.total = response.data.statusCount.total
            statistics.pending = response.data.statusCount.pending
            statistics.approved = response.data.statusCount.approved
            statistics.rejected = response.data.statusCount.rejected
          }
          
          listLoading.value = false
        } catch (error) {
          console.error('[Station Verify] 常规API获取失败, 尝试小程序API:', error)
          
          // 如果常规API失败，尝试使用小程序API
          try {
            const response = await getMiniprogramVerifications({
              page: listQuery.page,
              limit: listQuery.limit,
              status: listQuery.status || undefined,
              keyword: listQuery.keyword || undefined
            })
            
            console.log('[Station Verify] 获取成功 - 小程序API:', response)
            
            list.value = response.data.list
            total.value = response.data.total
            
            // 设置统计数据
            if (response.data.statusCount) {
              statistics.total = response.data.statusCount.total
              statistics.pending = response.data.statusCount.pending
              statistics.approved = response.data.statusCount.approved
              statistics.rejected = response.data.statusCount.rejected
            }
          } catch (mpError) {
            console.error('[Station Verify] 两种API都获取失败:', mpError)
            ElMessage.error('获取认证申请列表失败: ' + (mpError.message || '未知错误'))
          } finally {
            listLoading.value = false
          }
        }
      } catch (error) {
        console.error('[Station Verify] 获取列表时发生异常:', error)
        listLoading.value = false
        ElMessage.error('获取认证申请列表失败: ' + (error.message || '未知错误'))
      }
    }
    
    const handleFilter = () => {
      listQuery.page = 1
      getList()
    }
    
    const resetQuery = () => {
      dateRange.value = []
      Object.assign(listQuery, {
        page: 1,
        limit: 10,
        status: '',
        type: '',
        keyword: '',
        start_date: '',
        end_date: ''
      })
      getList()
    }
    
    const handleDateChange = (val) => {
      if (val) {
        listQuery.start_date = val[0]
        listQuery.end_date = val[1]
      } else {
        listQuery.start_date = ''
        listQuery.end_date = ''
      }
    }
    
    const getStatusType = (status) => {
      const statusMap = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger'
      }
      return statusMap[status] || 'info'
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝'
      }
      return statusMap[status] || '未知'
    }
    
    const getTypeText = (type) => {
      const typeMap = {
        'community': '社区驿站',
        'express': '快递驿站',
        'campus': '校园驿站',
        'office': '写字楼驿站',
        'commercial': '商业驿站'
      }
      return typeMap[type] || '未知'
    }
    
    const getTypeTagType = (type) => {
      const typeMap = {
        'community': '',  // 默认灰色
        'express': 'success',
        'campus': 'warning',
        'office': 'info',
        'commercial': 'danger'
      }
      return typeMap[type] || ''
    }
    
    const handleDetail = (row) => {
      detailLoading.value = true
      dialogVisible.value = true
      
      // 使用原始API获取详情
      getVerificationDetail(row.id).then(response => {
        if (response.code === 0) {
          detail.value = response.data
          detailLoading.value = false
        } else {
          // 如果原始API失败，尝试使用小程序API
          console.error('原始API获取详情失败:', response.message)
          tryMiniprogramDetailApi(row.id)
        }
      }).catch(error => {
        console.error('获取认证申请详情失败:', error)
        // 如果原始API失败，尝试使用小程序API
        tryMiniprogramDetailApi(row.id)
      })
    }
    
    // 尝试使用小程序API获取详情
    const tryMiniprogramDetailApi = (id) => {
      getMiniprogramVerificationDetail(id).then(response => {
        if (response.code === 0) {
          detail.value = response.data
          detailLoading.value = false
        } else {
          ElMessage.error(response.message || '获取认证申请详情失败')
          detailLoading.value = false
        }
      }).catch(error => {
        console.error('小程序API获取详情也失败了:', error)
        ElMessage.error('获取认证申请详情失败')
        detailLoading.value = false
      })
    }
    
    const previewImage = (url) => {
      previewImageUrl.value = url
      imagePreviewVisible.value = true
    }
    
    const handleApprove = (row) => {
      auditAction.value = 'approve'
      auditForm.id = row.id
      auditForm.status = 'approved'
      auditForm.remark = ''
      auditDialogVisible.value = true
    }
    
    const handleReject = (row) => {
      auditAction.value = 'reject'
      auditForm.id = row.id
      auditForm.status = 'rejected'
      auditForm.remark = ''
      auditDialogVisible.value = true
    }
    
    const submitAudit = () => {
      if (auditAction.value === 'reject' && !auditForm.remark) {
        ElMessage.warning('请填写拒绝原因')
        return
      }
      
      auditSubmitting.value = true
      
      reviewVerification(auditForm.id, {
        status: auditForm.status,
        remark: auditForm.remark
      }).then(response => {
        if (response.code === 0) {
          ElMessage({
            type: 'success',
            message: auditAction.value === 'approve' ? '驿站认证已通过' : '驿站认证已拒绝'
          })
          auditDialogVisible.value = false
          
          // 关闭详情对话框（如果打开）
          if (dialogVisible.value && detail.value.id === auditForm.id) {
            dialogVisible.value = false
          }
          
          // 刷新列表数据
          getList()
        } else {
          ElMessage.error(response.message || '审核操作失败')
        }
        auditSubmitting.value = false
      }).catch(error => {
        console.error('审核操作失败:', error)
        ElMessage.error('审核操作失败')
        auditSubmitting.value = false
      })
    }
    
    const exportData = () => {
      ElMessage.success('数据导出功能开发中')
    }
    
    onMounted(() => {
      getList()
    })
    
    return {
      formatDateTime,
      dateRange,
      list,
      total,
      listLoading,
      listQuery,
      statistics,
      statusOptions,
      typeOptions,
      dialogVisible,
      detailLoading,
      detail,
      auditDialogVisible,
      auditSubmitting,
      auditAction,
      auditForm,
      imagePreviewVisible,
      previewImageUrl,
      handleFilter,
      resetQuery,
      handleDateChange,
      getStatusType,
      getStatusText,
      getTypeText,
      getTypeTagType,
      handleDetail,
      previewImage,
      handleApprove,
      handleReject,
      submitAudit,
      exportData
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
  
  .filter-item {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .statistics-cards {
    display: flex;
    gap: 15px;
    
    .stat-card {
      padding: 15px;
      border-radius: 4px;
      width: 130px;
      text-align: center;
      
      &.primary {
        background-color: #ecf5ff;
        color: #409eff;
      }
      
      &.warning {
        background-color: #fdf6ec;
        color: #e6a23c;
      }
      
      &.success {
        background-color: #f0f9eb;
        color: #67c23a;
      }
      
      &.danger {
        background-color: #fef0f0;
        color: #f56c6c;
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .stat-label {
        font-size: 14px;
      }
    }
  }
}

.station-info {
  display: flex;
  align-items: flex-start;
  
  .station-detail {
    margin-left: 10px;
    
    .station-name {
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .station-address {
      color: #606266;
      font-size: 13px;
      margin-bottom: 5px;
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  
  .user-detail {
    margin-left: 10px;
    
    div:first-child {
      font-weight: bold;
    }
    
    div:last-child {
      font-size: 12px;
      color: #909399;
    }
  }
}

.contact-phone {
  color: #909399;
  font-size: 13px;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

.station-detail-dialog {
  min-height: 200px;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
  
  .image-section {
    flex: 1;
    min-width: 250px;
    
    .image-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .image-preview {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 180px;
      background-color: #f5f7fa;
      
      .no-image {
        color: #909399;
      }
    }
  }
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style> 