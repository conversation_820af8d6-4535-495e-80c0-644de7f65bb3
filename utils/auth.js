/**
 * 认证相关的工具函数
 */

const TokenKey = 'token';
const UserInfoKey = 'userInfo';

/**
 * 获取token
 * @returns {String} token字符串
 */
export function getToken() {
  return uni.getStorageSync(TokenKey);
}

/**
 * 设置token
 * @param {String} token token字符串
 */
export function setToken(token) {
  return uni.setStorageSync(TokenKey, token);
}

/**
 * 移除token
 */
export function removeToken() {
  return uni.removeStorageSync(TokenKey);
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  const userInfoStr = uni.getStorageSync(UserInfoKey);
  if (!userInfoStr) return null;
  
  // 处理可能的JSON字符串
  if (typeof userInfoStr === 'string') {
    try {
      return JSON.parse(userInfoStr);
    } catch (e) {
      console.error('解析用户信息失败:', e);
      return userInfoStr; // 如果解析失败，可能已经是对象
    }
  }
  return userInfoStr;
}

/**
 * 设置用户信息
 * @param {Object} userInfo 用户信息对象
 */
export function setUserInfo(userInfo) {
  return uni.setStorageSync(UserInfoKey, JSON.stringify(userInfo));
}

/**
 * 移除用户信息
 */
export function removeUserInfo() {
  return uni.removeStorageSync(UserInfoKey);
}

/**
 * 清除所有认证信息
 */
export function clearAuth() {
  removeToken();
  removeUserInfo();
}

/**
 * 是否已登录
 * @returns {Boolean} 是否已登录
 */
export function isLoggedIn() {
  return !!getToken();
} 