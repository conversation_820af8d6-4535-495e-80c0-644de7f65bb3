const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');

// 创建Express应用
const app = express();

// 基本中间件
app.use(cors({
  origin: true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'dist')));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// ==================== 推广系统API ====================
// 获取佣金排行
app.get('/api/admin/promotion/commission-ranking', (req, res) => {
  const { timeRange = 'month', limit = 10 } = req.query;
  
  const ranking = [
    {
      id: 1,
      nickname: '推广达人001',
      avatar: '/static/avatars/avatar1.jpg',
      level: 3,
      total_commission: 15678.90,
      commission_count: 234,
      invite_count: 89
    },
    {
      id: 2,
      nickname: '超级推广员',
      avatar: '/static/avatars/avatar2.jpg',
      level: 4,
      total_commission: 12345.67,
      commission_count: 198,
      invite_count: 76
    },
    {
      id: 3,
      nickname: '推广小能手',
      avatar: '/static/avatars/avatar3.jpg',
      level: 2,
      total_commission: 9876.54,
      commission_count: 156,
      invite_count: 65
    },
    {
      id: 4,
      nickname: '金牌推广',
      avatar: '/static/avatars/avatar4.jpg',
      level: 3,
      total_commission: 8765.43,
      commission_count: 134,
      invite_count: 54
    },
    {
      id: 5,
      nickname: '推广专家',
      avatar: '/static/avatars/avatar5.jpg',
      level: 2,
      total_commission: 7654.32,
      commission_count: 123,
      invite_count: 48
    }
  ];

  res.json({
    code: 0,
    message: '获取佣金排行成功',
    data: ranking.slice(0, parseInt(limit))
  });
});

// 获取推广用户排行
app.get('/api/admin/promotion/invites-ranking', (req, res) => {
  const { timeRange = 'month', limit = 10 } = req.query;
  
  const ranking = [
    {
      id: 1,
      nickname: '推广王者',
      avatar: '/static/avatars/avatar1.jpg',
      level: 4,
      invite_count: 156,
      total_commission: 12345.67,
      active_invites: 89
    },
    {
      id: 2,
      nickname: '邀请达人',
      avatar: '/static/avatars/avatar2.jpg',
      level: 3,
      invite_count: 134,
      total_commission: 9876.54,
      active_invites: 76
    },
    {
      id: 3,
      nickname: '推广高手',
      avatar: '/static/avatars/avatar3.jpg',
      level: 3,
      invite_count: 123,
      total_commission: 8765.43,
      active_invites: 65
    },
    {
      id: 4,
      nickname: '超级邀请',
      avatar: '/static/avatars/avatar4.jpg',
      level: 2,
      invite_count: 98,
      total_commission: 7654.32,
      active_invites: 54
    },
    {
      id: 5,
      nickname: '推广能手',
      avatar: '/static/avatars/avatar5.jpg',
      level: 2,
      invite_count: 87,
      total_commission: 6543.21,
      active_invites: 43
    }
  ];

  res.json({
    code: 0,
    message: '获取推广用户排行成功',
    data: ranking.slice(0, parseInt(limit))
  });
});

// 获取推广员等级分布
app.get('/api/admin/promotion/level-distribution', (req, res) => {
  const distribution = [
    { level: 1, name: '初级推广员', count: 456, percentage: 36.3 },
    { level: 2, name: '中级推广员', count: 389, percentage: 31.0 },
    { level: 3, name: '高级推广员', count: 267, percentage: 21.3 },
    { level: 4, name: '资深推广员', count: 144, percentage: 11.4 }
  ];

  res.json({
    code: 0,
    message: '获取推广员等级分布成功',
    data: distribution
  });
});

// ==================== 统计系统API ====================
// 获取仪表板数据
app.get('/api/statistics/dashboard', (req, res) => {
  const dashboardData = {
    totalUsers: 12567,
    totalStations: 1234,
    totalOrders: 45678,
    totalRevenue: 567890.12,
    userGrowth: 234,
    stationGrowth: 45,
    orderGrowth: 567,
    revenueGrowth: 12345.67
  };

  res.json({
    code: 0,
    message: '获取仪表板数据成功',
    data: dashboardData
  });
});

// 获取订单趋势
app.get('/api/statistics/orderTrend', (req, res) => {
  const trendData = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    data: [1200, 1900, 3000, 5000, 2000, 3000]
  };

  res.json({
    code: 0,
    message: '获取订单趋势成功',
    data: trendData
  });
});

// 获取用户增长趋势
app.get('/api/statistics/userGrowthTrend', (req, res) => {
  const trendData = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    data: [500, 800, 1200, 1800, 2200, 2800]
  };

  res.json({
    code: 0,
    message: '获取用户增长趋势成功',
    data: trendData
  });
});

// ==================== 驿站认证API ====================
// 获取驿站认证列表
app.post('/admin/miniprogram/verification/getList', (req, res) => {
  const { page = 1, pageSize = 20 } = req.body;
  
  const list = [
    {
      id: 1,
      stationName: '中关村驿站',
      ownerName: '张三',
      phone: '13800138001',
      address: '北京市海淀区中关村大街1号',
      status: 'pending',
      submitTime: '2024-06-01 10:00:00',
      images: ['/uploads/station1.jpg', '/uploads/station2.jpg']
    },
    {
      id: 2,
      stationName: '望京驿站',
      ownerName: '李四',
      phone: '13800138002',
      address: '北京市朝阳区望京街道',
      status: 'approved',
      submitTime: '2024-06-02 14:30:00',
      images: ['/uploads/station3.jpg']
    }
  ];

  res.json({
    code: 0,
    message: '获取驿站认证列表成功',
    data: {
      list,
      total: 50,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }
  });
});

// 获取驿站认证列表（GET方式）
app.get('/api/station/verify/list', (req, res) => {
  const { page = 1, pageSize = 20 } = req.query;
  
  const list = [
    {
      id: 1,
      stationName: '中关村驿站',
      ownerName: '张三',
      phone: '13800138001',
      address: '北京市海淀区中关村大街1号',
      status: 'pending',
      submitTime: '2024-06-01 10:00:00'
    },
    {
      id: 2,
      stationName: '望京驿站',
      ownerName: '李四',
      phone: '13800138002',
      address: '北京市朝阳区望京街道',
      status: 'approved',
      submitTime: '2024-06-02 14:30:00'
    }
  ];

  res.json({
    code: 0,
    message: '获取驿站认证列表成功',
    data: {
      list,
      total: 50,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }
  });
});

// ==================== 通用API ====================
// 测试路由
app.get('/api/test', (req, res) => {
  res.json({
    code: 0,
    message: '服务器运行正常',
    time: new Date().toISOString(),
    apis: [
      '/api/admin/promotion/commission-ranking',
      '/api/admin/promotion/invites-ranking',
      '/api/statistics/dashboard',
      '/api/station/verify/list',
      '/admin/miniprogram/verification/getList'
    ]
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: err.message
  });
});

// 所有其他请求返回index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// 启动服务器
const PORT = 3000;
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`=================================`);
  console.log(`服务器运行在端口: ${PORT}`);
  console.log(`后台管理系统: http://localhost:${PORT}`);
  console.log(`测试API: http://localhost:${PORT}/api/test`);
  console.log(`=================================`);
  console.log(`可用的API接口:`);
  console.log(`- 佣金排行: GET /api/admin/promotion/commission-ranking`);
  console.log(`- 推广用户排行: GET /api/admin/promotion/invites-ranking`);
  console.log(`- 仪表板数据: GET /api/statistics/dashboard`);
  console.log(`- 驿站认证: GET /api/station/verify/list`);
  console.log(`- 驿站认证: POST /admin/miniprogram/verification/getList`);
  console.log(`=================================`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('SIGTERM信号接收到，关闭服务器...');
  server.close(() => {
    console.log('HTTP服务器已关闭');
    process.exit(0);
  });
});
