const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// 尝试加载multer用于文件上传
let multer;
try {
  multer = require('multer');
} catch (error) {
  console.warn('multer未安装，将使用简单的文件上传处理');
}

// 尝试加载数据库模块
let sequelize, StationVerification, User;
try {
  const db = require('./database/sequelize');
  sequelize = db.sequelize;
  StationVerification = require('./database/models/StationVerification');
  User = require('./database/models/User');

  // 设置模型关联
  StationVerification.belongsTo(User, {
    foreignKey: 'userId',
    as: 'user'
  });
  User.hasMany(StationVerification, {
    foreignKey: 'userId',
    as: 'verifications'
  });

  console.log('数据库模块加载成功');
} catch (error) {
  console.warn('数据库模块加载失败，将使用模拟数据:', error.message);
}

// 辅助函数
function getStatusText(status) {
  const statusMap = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝'
  };
  return statusMap[status] || status;
}

function formatDate(date) {
  if (!date) return null;
  const d = new Date(date);
  return d.toISOString().slice(0, 19).replace('T', ' ');
}

function getStationTypeText(type) {
  const typeMap = {
    'community': '社区驿站',
    'express': '快递驿站',
    'campus': '校园驿站',
    'office': '写字楼驿站',
    'commercial': '商业驿站'
  };
  return typeMap[type] || type;
}

// 创建Express应用
const app = express();

// 基本中间件
app.use(cors({
  origin: true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'dist')));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// ==================== 推广系统API ====================
// 获取佣金排行
app.get('/api/admin/promotion/commission-ranking', (req, res) => {
  const { timeRange = 'month', limit = 10 } = req.query;
  
  const ranking = [
    {
      id: 1,
      nickname: '推广达人001',
      avatar: '/static/avatars/avatar1.jpg',
      level: 3,
      total_commission: 15678.90,
      commission_count: 234,
      invite_count: 89
    },
    {
      id: 2,
      nickname: '超级推广员',
      avatar: '/static/avatars/avatar2.jpg',
      level: 4,
      total_commission: 12345.67,
      commission_count: 198,
      invite_count: 76
    },
    {
      id: 3,
      nickname: '推广小能手',
      avatar: '/static/avatars/avatar3.jpg',
      level: 2,
      total_commission: 9876.54,
      commission_count: 156,
      invite_count: 65
    },
    {
      id: 4,
      nickname: '金牌推广',
      avatar: '/static/avatars/avatar4.jpg',
      level: 3,
      total_commission: 8765.43,
      commission_count: 134,
      invite_count: 54
    },
    {
      id: 5,
      nickname: '推广专家',
      avatar: '/static/avatars/avatar5.jpg',
      level: 2,
      total_commission: 7654.32,
      commission_count: 123,
      invite_count: 48
    }
  ];

  res.json({
    code: 0,
    message: '获取佣金排行成功',
    data: ranking.slice(0, parseInt(limit))
  });
});

// 获取推广用户排行
app.get('/api/admin/promotion/invites-ranking', (req, res) => {
  const { timeRange = 'month', limit = 10 } = req.query;
  
  const ranking = [
    {
      id: 1,
      nickname: '推广王者',
      avatar: '/static/avatars/avatar1.jpg',
      level: 4,
      invite_count: 156,
      total_commission: 12345.67,
      active_invites: 89
    },
    {
      id: 2,
      nickname: '邀请达人',
      avatar: '/static/avatars/avatar2.jpg',
      level: 3,
      invite_count: 134,
      total_commission: 9876.54,
      active_invites: 76
    },
    {
      id: 3,
      nickname: '推广高手',
      avatar: '/static/avatars/avatar3.jpg',
      level: 3,
      invite_count: 123,
      total_commission: 8765.43,
      active_invites: 65
    },
    {
      id: 4,
      nickname: '超级邀请',
      avatar: '/static/avatars/avatar4.jpg',
      level: 2,
      invite_count: 98,
      total_commission: 7654.32,
      active_invites: 54
    },
    {
      id: 5,
      nickname: '推广能手',
      avatar: '/static/avatars/avatar5.jpg',
      level: 2,
      invite_count: 87,
      total_commission: 6543.21,
      active_invites: 43
    }
  ];

  res.json({
    code: 0,
    message: '获取推广用户排行成功',
    data: ranking.slice(0, parseInt(limit))
  });
});

// 获取推广员等级分布
app.get('/api/admin/promotion/level-distribution', (req, res) => {
  const distribution = [
    { level: 1, name: '初级推广员', count: 456, percentage: 36.3 },
    { level: 2, name: '中级推广员', count: 389, percentage: 31.0 },
    { level: 3, name: '高级推广员', count: 267, percentage: 21.3 },
    { level: 4, name: '资深推广员', count: 144, percentage: 11.4 }
  ];

  res.json({
    code: 0,
    message: '获取推广员等级分布成功',
    data: distribution
  });
});

// ==================== 统计系统API ====================
// 获取仪表板数据
app.get('/api/statistics/dashboard', (req, res) => {
  const dashboardData = {
    totalUsers: 12567,
    totalStations: 1234,
    totalOrders: 45678,
    totalRevenue: 567890.12,
    userGrowth: 234,
    stationGrowth: 45,
    orderGrowth: 567,
    revenueGrowth: 12345.67
  };

  res.json({
    code: 0,
    message: '获取仪表板数据成功',
    data: dashboardData
  });
});

// 获取订单趋势
app.get('/api/statistics/orderTrend', (req, res) => {
  const trendData = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    data: [1200, 1900, 3000, 5000, 2000, 3000]
  };

  res.json({
    code: 0,
    message: '获取订单趋势成功',
    data: trendData
  });
});

// 获取用户增长趋势
app.get('/api/statistics/userGrowthTrend', (req, res) => {
  const trendData = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    data: [500, 800, 1200, 1800, 2200, 2800]
  };

  res.json({
    code: 0,
    message: '获取用户增长趋势成功',
    data: trendData
  });
});

// 获取订单类型分布
app.get('/api/statistics/orderTypeDistribution', (req, res) => {
  const distribution = [
    { name: '社区驿站', value: 135, color: '#409EFF' },
    { name: '快递驿站', value: 86, color: '#67C23A' },
    { name: '校园驿站', value: 45, color: '#E6A23C' },
    { name: '写字楼驿站', value: 68, color: '#F56C6C' },
    { name: '商业驿站', value: 24, color: '#909399' }
  ];

  res.json({
    code: 0,
    message: '获取订单类型分布成功',
    data: distribution
  });
});

// 获取用户地区分布
app.get('/api/statistics/userRegionDistribution', (req, res) => {
  const distribution = [
    { name: '北京', value: 2345 },
    { name: '上海', value: 1876 },
    { name: '广州', value: 1543 },
    { name: '深圳', value: 1234 },
    { name: '杭州', value: 987 },
    { name: '其他', value: 4567 }
  ];

  res.json({
    code: 0,
    message: '获取用户地区分布成功',
    data: distribution
  });
});

// 获取热门驿站排行
app.get('/api/statistics/hotStations', (req, res) => {
  const hotStations = [
    { id: 1, name: '中关村驿站', orders: 1234, revenue: 45678.90 },
    { id: 2, name: '望京驿站', orders: 1156, revenue: 42345.67 },
    { id: 3, name: '国贸驿站', orders: 1089, revenue: 39876.54 },
    { id: 4, name: '三里屯驿站', orders: 987, revenue: 36543.21 },
    { id: 5, name: '西单驿站', orders: 876, revenue: 32109.87 }
  ];

  res.json({
    code: 0,
    message: '获取热门驿站排行成功',
    data: hotStations
  });
});

// ==================== 驿站认证API ====================
// 获取驿站认证列表
app.post('/admin/miniprogram/verification/getList', async (req, res) => {
  try {
    const { page = 1, pageSize = 20, status } = req.body;

    // 如果数据库可用，从数据库获取数据
    if (StationVerification && sequelize) {
      const { Op } = require('sequelize');
      const offset = (page - 1) * pageSize;
      const where = {};

      if (status && status !== 'all') {
        where.status = status;
      }

      const { count, rows } = await StationVerification.findAndCountAll({
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'phone', 'avatar'],
            required: false
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(pageSize),
        offset: parseInt(offset)
      });

      // 格式化数据
      const formattedRows = rows.map(row => {
        const item = row.toJSON();
        return {
          id: item.id,
          stationName: item.stationName,
          stationCode: item.stationCode,
          stationType: item.stationType,
          brand: item.brand,
          ownerName: item.contactName,
          phone: item.contactPhone,
          address: item.address,
          contactName: item.contactName,
          contactPhone: item.contactPhone,
          status: item.status,
          statusText: getStatusText(item.status),
          submitTime: formatDate(item.createdAt),
          createdAt: formatDate(item.createdAt),
          reviewedAt: item.reviewedAt ? formatDate(item.reviewedAt) : null,
          businessLicense: item.businessLicense,
          storefront: item.storefront,
          interior: item.interior,
          remark: item.remark,
          images: [item.storefront, item.interior].filter(Boolean)
        };
      });

      // 获取状态统计
      const statusCount = {
        pending: await StationVerification.count({ where: { status: 'pending' } }),
        approved: await StationVerification.count({ where: { status: 'approved' } }),
        rejected: await StationVerification.count({ where: { status: 'rejected' } }),
        total: count
      };

      res.json({
        code: 0,
        message: '获取驿站认证列表成功',
        data: {
          list: formattedRows,
          total: count,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          statusCount
        }
      });
    } else {
      // 使用模拟数据
      const allList = [
        {
          id: 1,
          stationName: '中关村驿站',
          stationCode: 'ZGC001',
          stationType: 'community',
          brand: '菜鸟驿站',
          ownerName: '张三',
          phone: '***********',
          address: '北京市海淀区中关村大街1号',
          contactName: '张三',
          contactPhone: '***********',
          status: 'pending',
          statusText: '待审核',
          submitTime: '2024-06-01 10:00:00',
          createdAt: '2024-06-01 10:00:00',
          businessLicense: '/uploads/license1.jpg',
          storefront: '/uploads/storefront1.jpg',
          interior: '/uploads/interior1.jpg',
          images: ['/uploads/station1.jpg', '/uploads/station2.jpg']
        }
      ];

      // 根据状态过滤
      let filteredList = allList;
      if (status && status !== 'all') {
        filteredList = allList.filter(item => item.status === status);
      }

      // 分页
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const list = filteredList.slice(start, end);

      res.json({
        code: 0,
        message: '获取驿站认证列表成功（模拟数据）',
        data: {
          list,
          total: filteredList.length,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          statusCount: {
            pending: allList.filter(item => item.status === 'pending').length,
            approved: allList.filter(item => item.status === 'approved').length,
            rejected: allList.filter(item => item.status === 'rejected').length,
            total: allList.length
          }
        }
      });
    }
  } catch (error) {
    console.error('获取驿站认证列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取驿站认证列表失败',
      error: error.message
    });
  }
});

// 获取驿站认证详情
app.post('/admin/miniprogram/verification/getDetail', async (req, res) => {
  try {
    const { id } = req.body;

    // 如果数据库可用，从数据库获取数据
    if (StationVerification && sequelize) {
      const verification = await StationVerification.findByPk(id, {
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'phone', 'avatar'],
            required: false
          }
        ]
      });

      if (!verification) {
        return res.status(404).json({
          code: 404,
          message: '认证申请不存在'
        });
      }

      const item = verification.toJSON();
      const detail = {
        id: item.id,
        stationName: item.stationName,
        stationCode: item.stationCode,
        stationType: item.stationType,
        stationTypeText: getStationTypeText(item.stationType),
        brand: item.brand,
        ownerName: item.contactName,
        phone: item.contactPhone,
        address: item.address,
        contactName: item.contactName,
        contactPhone: item.contactPhone,
        longitude: item.longitude,
        latitude: item.latitude,
        status: item.status,
        statusText: getStatusText(item.status),
        submitTime: formatDate(item.createdAt),
        createdAt: formatDate(item.createdAt),
        reviewedAt: item.reviewedAt ? formatDate(item.reviewedAt) : null,
        businessLicense: item.businessLicense,
        authorization: item.authorization,
        storefront: item.storefront,
        interior: item.interior,
        remark: item.remark,
        user: item.user || {
          id: item.userId,
          nickname: '未知用户',
          phone: item.contactPhone,
          avatar: null
        }
      };

      res.json({
        code: 0,
        message: '获取认证申请详情成功',
        data: detail
      });
    } else {
      // 使用模拟数据
      const mockDetail = {
        id: parseInt(id),
        stationName: '模拟驿站',
        stationCode: 'MOCK001',
        stationType: 'community',
        stationTypeText: '社区驿站',
        brand: '模拟品牌',
        ownerName: '模拟用户',
        phone: '***********',
        address: '模拟地址',
        contactName: '模拟用户',
        contactPhone: '***********',
        longitude: 116.3074,
        latitude: 39.9042,
        status: 'pending',
        statusText: '待审核',
        submitTime: '2024-06-01 10:00:00',
        createdAt: '2024-06-01 10:00:00',
        businessLicense: '/uploads/mock-license.jpg',
        authorization: '/uploads/mock-auth.jpg',
        storefront: '/uploads/mock-storefront.jpg',
        interior: '/uploads/mock-interior.jpg',
        user: {
          id: 1,
          nickname: '模拟用户',
          phone: '***********',
          avatar: '/uploads/mock-avatar.jpg'
        }
      };

      res.json({
        code: 0,
        message: '获取认证申请详情成功（模拟数据）',
        data: mockDetail
      });
    }
  } catch (error) {
    console.error('获取驿站认证详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取驿站认证详情失败',
      error: error.message
    });
  }
});

// 获取驿站认证列表（GET方式）
app.get('/api/station/verify/list', (req, res) => {
  const { page = 1, pageSize = 20 } = req.query;
  
  const list = [
    {
      id: 1,
      stationName: '中关村驿站',
      ownerName: '张三',
      phone: '***********',
      address: '北京市海淀区中关村大街1号',
      status: 'pending',
      submitTime: '2024-06-01 10:00:00'
    },
    {
      id: 2,
      stationName: '望京驿站',
      ownerName: '李四',
      phone: '13800138002',
      address: '北京市朝阳区望京街道',
      status: 'approved',
      submitTime: '2024-06-02 14:30:00'
    }
  ];

  res.json({
    code: 0,
    message: '获取驿站认证列表成功',
    data: {
      list,
      total: 50,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }
  });
});

// ==================== 认证API ====================
// 获取登录状态
app.get('/api/auth/status', (req, res) => {
  // 从请求头中获取token
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      code: 401,
      message: '未提供有效的认证信息'
    });
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    return res.status(401).json({
      code: 401,
      message: '未提供有效的认证信息'
    });
  }

  // 简单的token验证（在实际项目中应该使用JWT验证）
  // 这里我们返回一个模拟的用户信息
  res.json({
    code: 0,
    message: '已登录',
    data: {
      user: {
        id: 1,
        phone: '***********',
        nickname: '测试用户',
        avatar: null,
        createdAt: new Date().toISOString()
      }
    }
  });
});

// 微信登录接口
app.post('/api/auth/wx-login', (req, res) => {
  const { code, userInfo } = req.body;

  // 模拟微信登录成功
  const mockToken = 'mock_token_' + Date.now();
  const mockUser = {
    id: Math.floor(Math.random() * 1000),
    openid: 'mock_openid_' + Date.now(),
    nickname: userInfo?.nickName || '微信用户',
    avatar: userInfo?.avatarUrl || null,
    phone: null,
    createdAt: new Date().toISOString()
  };

  res.json({
    code: 0,
    message: '登录成功',
    data: {
      token: mockToken,
      user: mockUser,
      isNewUser: true
    }
  });
});

// ==================== 文件上传API ====================
// 上传验证图片
app.post('/api/upload/verification', (req, res) => {
  // 简单的模拟上传处理
  // 在实际项目中，这里应该使用multer处理文件上传

  // 模拟生成文件名和URL
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 15);
  const fileName = `${randomId}-${timestamp}.png`;
  const fileUrl = `/uploads/common/${fileName}`;

  // 模拟上传成功
  setTimeout(() => {
    res.json({
      code: 0,
      message: '上传成功',
      data: {
        url: fileUrl,
        filename: fileName,
        originalname: 'uploaded_image.png',
        mimetype: 'image/png',
        size: Math.floor(Math.random() * 1000000) + 100000 // 随机文件大小
      }
    });
  }, 500); // 模拟上传延迟
});

// 通用文件上传接口
app.post('/api/upload', (req, res) => {
  // 模拟文件上传
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 15);
  const fileName = `${randomId}-${timestamp}.jpg`;
  const fileUrl = `/uploads/common/${fileName}`;

  setTimeout(() => {
    res.json({
      code: 0,
      message: '上传成功',
      data: {
        url: fileUrl,
        filename: fileName,
        originalname: 'uploaded_file.jpg',
        mimetype: 'image/jpeg',
        size: Math.floor(Math.random() * 1000000) + 100000
      }
    });
  }, 300);
});

// ==================== 通用API ====================
// 测试路由
app.get('/api/test', (req, res) => {
  res.json({
    code: 0,
    message: '服务器运行正常',
    time: new Date().toISOString(),
    apis: [
      '/api/admin/promotion/commission-ranking',
      '/api/admin/promotion/invites-ranking',
      '/api/statistics/dashboard',
      '/api/station/verify/list',
      '/admin/miniprogram/verification/getList',
      '/api/auth/status',
      '/api/auth/wx-login'
    ]
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: err.message
  });
});

// 所有其他请求返回index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// 启动服务器
const PORT = 3000;
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`=================================`);
  console.log(`服务器运行在端口: ${PORT}`);
  console.log(`后台管理系统: http://localhost:${PORT}`);
  console.log(`测试API: http://localhost:${PORT}/api/test`);
  console.log(`=================================`);
  console.log(`可用的API接口:`);
  console.log(`- 佣金排行: GET /api/admin/promotion/commission-ranking`);
  console.log(`- 推广用户排行: GET /api/admin/promotion/invites-ranking`);
  console.log(`- 仪表板数据: GET /api/statistics/dashboard`);
  console.log(`- 驿站认证: GET /api/station/verify/list`);
  console.log(`- 驿站认证: POST /admin/miniprogram/verification/getList`);
  console.log(`=================================`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('SIGTERM信号接收到，关闭服务器...');
  server.close(() => {
    console.log('HTTP服务器已关闭');
    process.exit(0);
  });
});
