const express = require('express');
const promotionController = require('../controllers/promotionController');

const router = express.Router();

// 推广统计相关路由
router.get('/overview', promotionController.getPromotionOverview);
router.get('/commission-ranking', promotionController.getCommissionRanking);
router.get('/invites-ranking', promotionController.getInvitesRanking);
router.get('/level-distribution', promotionController.getPromoterLevelDistribution);

// 推广员管理
router.get('/promoters', promotionController.getPromoterList);
router.get('/promoters/:id', promotionController.getPromoterDetail);
router.post('/promoters/:id/approve', promotionController.approvePromoter);
router.post('/promoters/:id/reject', promotionController.rejectPromoter);

// 推广码管理
router.get('/codes', promotionController.getPromotionCodes);
router.post('/codes', promotionController.createPromotionCode);
router.put('/codes/:id', promotionController.updatePromotionCode);
router.delete('/codes/:id', promotionController.deletePromotionCode);

// 佣金管理
router.get('/commissions', promotionController.getCommissionRecords);
router.get('/commissions/stats', promotionController.getCommissionStats);

// 提现管理
router.get('/withdrawals', promotionController.getWithdrawalList);
router.post('/withdrawals/:id/approve', promotionController.approveWithdrawal);
router.post('/withdrawals/:id/reject', promotionController.rejectWithdrawal);

module.exports = router;
