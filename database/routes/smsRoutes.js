const express = require('express');
const smsController = require('../controllers/smsController');
const { validateRequest } = require('../middleware/validationMiddleware');

const router = express.Router();

/**
 * @api {post} /api/sms/send 发送短信验证码
 * @apiDescription 发送短信验证码到指定手机号
 * @apiName SendSmsCode
 * @apiGroup SMS
 * 
 * @apiParam {String} phone 手机号码
 * @apiParam {String} type 短信类型 (login/register/resetPassword)
 * 
 * @apiSuccess {Number} code 状态码 0表示成功
 * @apiSuccess {String} message 状态信息
 * @apiSuccess {Object} data 返回数据
 * @apiSuccess {Number} data.expireTime 验证码有效期（秒）
 */
router.post(
  '/send',
  validateRequest({
    body: {
      phone: { type: 'string', required: true },
      type: { type: 'string', required: true, enum: ['login', 'register', 'resetPassword'] }
    }
  }),
  smsController.sendSmsCode
);

/**
 * @api {get} /api/sms/code 获取验证码（仅开发环境）
 * @apiDescription 获取之前发送的验证码（仅开发环境使用）
 * @apiName GetSmsCode
 * @apiGroup SMS
 * 
 * @apiParam {String} phone 手机号码
 * 
 * @apiSuccess {Number} code 状态码 0表示成功
 * @apiSuccess {String} message 状态信息
 * @apiSuccess {Object} data 返回数据
 * @apiSuccess {String} data.code 验证码
 * @apiSuccess {Number} data.expireTime 剩余有效期（秒）
 */
router.get('/code', smsController.getCodeForDev);

module.exports = router; 