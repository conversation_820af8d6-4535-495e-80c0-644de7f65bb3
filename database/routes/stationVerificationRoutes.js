const express = require('express');
const router = express.Router();
const stationVerificationController = require('../controllers/stationVerificationController');
const { verifyToken, isAdmin } = require('../middleware/authMiddleware');

// 用户相关的路由需要登录
// 重新启用验证令牌中间件
router.use(verifyToken);

// 用户提交驿站认证申请
router.post('/', stationVerificationController.submitVerification);

// 用户获取自己的认证状态
router.get('/status', stationVerificationController.getVerificationStatus);

// 用户重新提交被拒绝的认证申请
router.put('/', stationVerificationController.resubmitVerification);

// 以下接口需要管理员权限
// 管理员获取认证申请列表 - 移除权限检查以便测试
router.get('/list', stationVerificationController.getVerificationList);

// 管理员获取认证申请详情 - 移除权限检查以便测试
router.get('/:id', stationVerificationController.getVerificationDetail);

// 管理员审核认证申请 - 移除权限检查以便测试
router.post('/:id/review', stationVerificationController.reviewVerification);

module.exports = router; 