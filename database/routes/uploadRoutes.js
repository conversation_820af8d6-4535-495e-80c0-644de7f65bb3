const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/uploadController');
const { verifyToken } = require('../middleware/authMiddleware');

// 需要登录的路由
router.use(verifyToken);

// 上传验证图片
router.post('/verification', uploadController.uploadVerificationImage);

// 删除上传的图片
router.delete('/', uploadController.deleteUploadedFile);

module.exports = router; 