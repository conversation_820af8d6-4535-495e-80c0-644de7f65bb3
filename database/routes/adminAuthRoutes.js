const express = require('express');
const router = express.Router();
const adminAuthController = require('../controllers/adminAuthController');
const { verifyToken, isAdmin } = require('../middleware/authMiddleware');

// 管理员登录路由 - 不需要认证
router.post('/login', adminAuthController.adminLogin);
router.post('/logout', adminAuthController.adminLogout);

// 需要认证和管理员权限的路由
router.use(verifyToken);
router.use(isAdmin);

// 获取管理员信息
router.get('/info', adminAuthController.getAdminInfo);

// 刷新管理员token
router.post('/refresh-token', adminAuthController.refreshAdminToken);

module.exports = router; 