const express = require('express');
const authController = require('../controllers/authController');
const { verifyToken } = require('../middleware/authMiddleware');

const router = express.Router();

// 公开路由
router.post('/register', authController.register);
router.post('/login', authController.login);
router.get('/check-phone', authController.checkPhone);
router.post('/reset-password', authController.resetPassword);
router.post('/logout', authController.logout);

// 微信登录
router.post('/wx-login', authController.wxLogin);

// 需要认证的路由
router.post('/refresh-token', verifyToken, authController.refreshToken);
router.get('/status', verifyToken, authController.getLoginStatus);
router.put('/profile', verifyToken, authController.updateProfile);

module.exports = router; 