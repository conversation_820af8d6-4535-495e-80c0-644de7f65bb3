// 导入必要的模块
const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

// 从环境变量获取数据库配置
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = parseInt(process.env.DB_PORT) || 3306;
const DB_USER = process.env.DB_USER || 'root';
const DB_PASSWORD = process.env.DB_PASSWORD || '';  // 使用环境变量，无默认密码
const DB_NAME = process.env.DB_NAME || 'yizhanbang';
const DB_DIALECT = process.env.DB_DIALECT || 'mysql';

console.log('数据库配置:', {
  host: DB_HOST,
  port: DB_PORT,
  user: DB_USER,
  password: DB_PASSWORD ? '******' : 'no password',
  database: DB_NAME,
  dialect: DB_DIALECT
});

// 创建Sequelize实例
const sequelize = new Sequelize(DB_NAME, DB_USER, DB_PASSWORD, {
  host: DB_HOST,
  port: DB_PORT,
  dialect: DB_DIALECT,
  timezone: '+08:00',
  define: {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci',
    timestamps: true,
    underscored: true,
    freezeTableName: false
  },
  // 添加dialectOptions配置，处理严格模式下的日期问题
  dialectOptions: {
    dateStrings: true,
    typeCast: true,
    // 支持在严格模式下使用默认时间戳
    timezone: '+08:00'
  },
  logging: console.log,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  }
});

// 导出Sequelize实例
module.exports = {
  sequelize,
  Sequelize
}; 