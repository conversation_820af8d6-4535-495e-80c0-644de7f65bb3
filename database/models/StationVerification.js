const { DataTypes } = require('sequelize');
const { sequelize } = require('../sequelize');

const StationVerification = sequelize.define('StationVerification', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id',
    comment: '申请用户ID'
  },
  stationName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'station_name',
    comment: '驿站名称'
  },
  stationCode: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'station_code',
    comment: '驿站编码'
  },
  stationType: {
    type: DataTypes.ENUM('community', 'express', 'campus', 'office', 'commercial'),
    allowNull: false,
    field: 'station_type',
    defaultValue: 'community',
    comment: '驿站类型：社区、快递、校园、写字楼、商业'
  },
  brand: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '所属品牌'
  },
  address: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: '详细地址'
  },
  longitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true,
    comment: '经度'
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true,
    comment: '纬度'
  },
  contactName: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'contact_name',
    comment: '联系人姓名'
  },
  contactPhone: {
    type: DataTypes.STRING(20),
    allowNull: false,
    field: 'contact_phone',
    comment: '联系电话'
  },
  businessLicense: {
    type: DataTypes.STRING(500),
    allowNull: false,
    field: 'business_license',
    comment: '营业执照图片URL'
  },
  authorization: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '授权书图片URL'
  },
  storefront: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: '门头照图片URL'
  },
  interior: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: '内部照图片URL'
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '审核状态'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '审核备注'
  },
  reviewedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'reviewed_by',
    comment: '审核人ID'
  },
  reviewedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'reviewed_at',
    comment: '审核时间'
  },
  createdAt: {
    type: DataTypes.DATE,
    field: 'created_at'
  },
  updatedAt: {
    type: DataTypes.DATE,
    field: 'updated_at'
  }
}, {
  tableName: 'station_verifications',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['reviewed_by']
    }
  ]
});

module.exports = StationVerification; 