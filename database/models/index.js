/**
 * 模型关联关系定义
 */
const User = require('./User');
const Station = require('./Station');
const StationVerification = require('./StationVerification');
const Order = require('./Order');

// 用户与驿站的关系：一个用户可以拥有多个驿站
User.hasMany(Station, {
  foreignKey: 'ownerId',
  as: 'stations'
});

Station.belongsTo(User, {
  foreignKey: 'ownerId',
  as: 'owner'
});

// 用户与驿站认证的关系：一个用户可以有多个认证申请记录
User.hasMany(StationVerification, {
  foreignKey: 'userId',
  as: 'stationVerifications'
});

StationVerification.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

// 审核人与驿站认证的关系
User.hasMany(StationVerification, {
  foreignKey: 'reviewedBy',
  as: 'reviewedVerifications'
});

StationVerification.belongsTo(User, {
  foreignKey: 'reviewedBy',
  as: 'reviewer'
});

// 导出模型
module.exports = {
  User,
  Station,
  StationVerification,
  Order
}; 