const { DataTypes } = require('sequelize');
const { sequelize } = require('../sequelize');

const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderNo: {
    type: DataTypes.STRING(50),
    allowNull: true,
    unique: true,
    field: 'order_no',
    comment: '订单号'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'user_id',
    comment: '用户ID'
  },
  stationId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'station_id',
    comment: '驿站ID'
  },
  orderType: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'order_type',
    comment: '订单类型'
  },
  orderStatus: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    field: 'order_status',
    comment: '订单状态'
  },
  payStatus: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    field: 'pay_status',
    comment: '支付状态'
  },
  payType: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'pay_type',
    comment: '支付方式'
  },
  totalAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    field: 'total_amount',
    comment: '订单总金额'
  },
  payAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    field: 'pay_amount',
    comment: '支付金额'
  },
  discountAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    field: 'discount_amount',
    comment: '优惠金额'
  },
  shippingFee: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    field: 'shipping_fee',
    comment: '运费'
  },
  remark: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '备注'
  },
  paidAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'paid_at',
    comment: '支付时间'
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'completed_at',
    comment: '完成时间'
  },
  cancelledAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'cancelled_at',
    comment: '取消时间'
  },
  refundStatus: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    field: 'refund_status',
    comment: '退款状态'
  },
  refundTime: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'refund_time',
    comment: '退款时间'
  },
  refundAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    field: 'refund_amount',
    comment: '退款金额'
  },
  createdAt: {
    type: DataTypes.DATE,
    field: 'created_at'
  },
  updatedAt: {
    type: DataTypes.DATE,
    field: 'updated_at'
  }
}, {
  tableName: 'orders',
  timestamps: true,
  underscored: true,
  paranoid: false, // 不使用软删除，因为表中没有deleted_at列
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['station_id']
    },
    {
      fields: ['order_status']
    }
  ]
});

module.exports = Order;
