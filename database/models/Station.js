const { DataTypes } = require('sequelize');
const { sequelize } = require('../sequelize');

const Station = sequelize.define('Station', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '驿站名称'
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '驿站编码'
  },
  type: {
    type: DataTypes.ENUM('community', 'express', 'campus', 'office', 'commercial'),
    allowNull: false,
    defaultValue: 'community',
    comment: '驿站类型：社区、快递、校园、写字楼、商业'
  },
  ownerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'owner_id',
    comment: '站长用户ID'
  },
  address: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: '详细地址'
  },
  province: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '省份'
  },
  city: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '城市'
  },
  district: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '区县'
  },
  longitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true,
    comment: '经度'
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true,
    comment: '纬度'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '联系电话'
  },
  businessHours: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'business_hours',
    comment: '营业时间'
  },
  services: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '提供的服务列表'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '驿站图片'
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected', 'suspended', 'closed'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '驿站状态'
  },
  verifiedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'verified_at',
    comment: '审核通过时间'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '驿站描述'
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    defaultValue: 5.00,
    comment: '评分'
  },
  orderCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'order_count',
    defaultValue: 0,
    comment: '订单数量'
  },
  revenue: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '总收入'
  },
  createdAt: {
    type: DataTypes.DATE,
    field: 'created_at'
  },
  updatedAt: {
    type: DataTypes.DATE,
    field: 'updated_at'
  },
  deletedAt: {
    type: DataTypes.DATE,
    field: 'deleted_at'
  }
}, {
  tableName: 'stations',
  timestamps: true,
  underscored: true,
  paranoid: true, // 使用软删除
  indexes: [
    {
      fields: ['owner_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['city']
    },
    {
      fields: ['district']
    },
    {
      fields: ['longitude', 'latitude']
    }
  ]
});

module.exports = Station;
