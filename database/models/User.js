const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../sequelize');

// 创建用户模型
const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  // 基本信息
  nickname: {
    type: DataTypes.STRING,
    allowNull: true
  },
  avatar: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: ''
  },
  gender: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0 // 0: 未知, 1: 男, 2: 女
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  password: {
    type: DataTypes.STRING,
    allowNull: true
  },
  
  // 微信信息
  openid: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  unionid: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  
  // 用户状态
  isVerified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    field: 'is_verified'
  },
  
  // 驿站认证状态
  isStationVerified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    field: 'is_station_verified',
    comment: '是否已通过驿站认证'
  },
  
  isStationVerifying: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    field: 'is_station_verifying',
    comment: '是否正在进行驿站认证'
  },
  
  // 是否是管理员
  isAdmin: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    field: 'is_admin',
    comment: '是否是管理员用户'
  },
  
  // 用户角色 - 存储为JSON数组
  roles: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: ['user']
  },
  
  // 用户标签 - 存储为JSON数组
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  
  // 登录信息
  lastLoginTime: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'last_login_time'
  },
  
  loginCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'login_count'
  },
  
  // 时间戳
  createdAt: {
    type: DataTypes.DATE,
    field: 'created_at'
  },
  updatedAt: {
    type: DataTypes.DATE,
    field: 'updated_at'
  }
}, {
  // 模型选项
  tableName: 'users',
  timestamps: true, // 创建 createdAt 和 updatedAt
  underscored: true, // 使用下划线命名约定
  paranoid: false, // 不使用软删除
  indexes: [
    {
      fields: ['created_at']
    },
    {
      fields: ['updated_at']
    },
    {
      fields: ['phone']
    },
    {
      fields: ['email']
    },
    {
      fields: ['openid']
    },
    {
      fields: ['is_station_verified']
    }
  ],
  defaultScope: {
    attributes: { exclude: ['password'] }
  },
  scopes: {
    withPassword: {
      attributes: { include: ['password'] }
    }
  }
});

// 密码加密的钩子
User.beforeCreate(async (user) => {
  if (user.password) {
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(user.password, salt);
  }
  
  // 如果用户角色包含admin，则设置isAdmin为true
  if (user.roles && user.roles.includes('admin')) {
    user.isAdmin = true;
  }
});

User.beforeUpdate(async (user) => {
  if (user.changed('password')) {
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(user.password, salt);
  }
  
  // 如果用户角色发生变化，更新isAdmin
  if (user.changed('roles')) {
    user.isAdmin = user.roles && user.roles.includes('admin');
  }
});

// 实例方法
User.prototype.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

module.exports = User; 
