const jwt = require('jsonwebtoken');
const User = require('../models/User');
const bcrypt = require('bcryptjs');
const config = require('../../config');

/**
 * 生成管理员JWT令牌
 * @param {Object} admin 管理员对象
 * @returns {String} JWT令牌
 */
const generateAdminToken = (admin) => {
  console.log('====== 生成管理员Token开始 ======');
  console.log('管理员信息:', {
    id: admin.id,
    phone: admin.phone,
    isAdmin: true,
    roles: admin.roles || ['admin']
  });
  
  const tokenPayload = { 
    id: admin.id,
    phone: admin.phone,
    isAdmin: true,
    roles: admin.roles || ['admin'],
    type: 'admin' // 明确标记为管理员token
  };
  
  console.log('Token载荷:', tokenPayload);
  console.log('JWT密钥:', config.jwt.secret);
  console.log('JWT过期时间:', config.jwt.expiresIn);
  
  const token = jwt.sign(
    tokenPayload,
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  );
  
  console.log('生成的管理员Token:', token.substring(0, 20) + '...');
  console.log('====== 生成管理员Token结束 ======');
  
  return token;
};

/**
 * 管理员登录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.adminLogin = async (req, res) => {
  try {
    console.log('====== 管理员登录请求开始 ======');
    console.log('登录数据:', req.body);
    
    const { username, password } = req.body;

    // 验证请求数据
    if (!username || !password) {
      console.log('管理员登录失败: 用户名或密码为空');
      return res.status(400).json({
        code: 400,
        message: '用户名和密码不能为空'
      });
    }

    // 查找管理员用户
    const admin = await User.scope('withPassword').findOne({
      where: { 
        phone: username,
        isAdmin: true
      }
    });

    // 如果没找到管理员
    if (!admin) {
      console.log('管理员登录失败: 用户不存在或不是管理员 -', username);
      return res.status(401).json({
        code: 401,
        message: '用户名或密码错误'
      });
    }

    console.log('找到管理员:', admin.id, admin.phone, '角色:', admin.roles);

    // 验证密码
    if (!admin.password) {
      console.log('管理员登录失败: 管理员未设置密码');
      return res.status(401).json({
        code: 401,
        message: '管理员账户未设置密码'
      });
    }

    // 验证密码是否正确
    const isPasswordValid = await admin.comparePassword(password);
    if (!isPasswordValid) {
      console.log('管理员登录失败: 密码错误');
      return res.status(401).json({
        code: 401,
        message: '用户名或密码错误'
      });
    }

    console.log('管理员密码验证成功');

    // 更新登录信息
    admin.lastLoginIp = req.ip;
    admin.lastLoginTime = new Date();
    admin.loginCount = (admin.loginCount || 0) + 1;
    await admin.save();

    // 生成管理员token
    const token = generateAdminToken(admin);

    // 返回管理员信息和token
    console.log('====== 管理员登录成功 ======');
    res.json({
      code: 0,
      message: '登录成功',
      data: {
        token,
        admin: {
          id: admin.id,
          phone: admin.phone,
          nickname: admin.nickname,
          avatar: admin.avatar,
          isAdmin: true,
          roles: admin.roles,
          permissions: ['*'] // 管理员默认拥有所有权限
        }
      }
    });
  } catch (error) {
    console.error('管理员登录异常:', error);
    res.status(500).json({
      code: 500,
      message: '登录失败',
      error: error.message
    });
  }
};

/**
 * 获取管理员信息
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getAdminInfo = async (req, res) => {
  try {
    // 用户信息已在auth中间件中添加到req.user
    const admin = req.user;

    // 验证是否为管理员
    if (!admin.isAdmin && (!admin.roles || !admin.roles.includes('admin'))) {
      return res.status(403).json({
        code: 403,
        message: '无管理员权限'
      });
    }

    res.json({
      code: 0,
      message: '获取管理员信息成功',
      data: {
        id: admin.id,
        nickname: admin.nickname || admin.phone,
        avatar: admin.avatar,
        phone: admin.phone,
        roles: admin.roles || ['admin'],
        isAdmin: true,
        permissions: ['*'], // 管理员默认拥有所有权限
        createdAt: admin.createdAt
      }
    });
  } catch (error) {
    console.error('获取管理员信息错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

/**
 * 管理员登出
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.adminLogout = async (req, res) => {
  try {
    // 客户端负责删除token，服务端无需特殊处理
    res.json({
      code: 0,
      message: '管理员登出成功'
    });
  } catch (error) {
    console.error('管理员登出错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

/**
 * 刷新管理员Token
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.refreshAdminToken = async (req, res) => {
  try {
    // 用户信息已在auth中间件中添加到req.user
    const admin = req.user;
    
    // 验证是否为管理员
    if (!admin.isAdmin && (!admin.roles || !admin.roles.includes('admin'))) {
      return res.status(403).json({
        code: 403,
        message: '无管理员权限'
      });
    }
    
    // 生成新token
    const token = generateAdminToken(admin);
    
    res.json({
      code: 0,
      message: '管理员Token刷新成功',
      data: {
        token
      }
    });
  } catch (error) {
    console.error('刷新管理员Token错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
}; 