const jwt = require('jsonwebtoken');
const axios = require('axios');
const User = require('../models/User');
const { Op } = require('sequelize');
const bcrypt = require('bcryptjs');
const smsController = require('./smsController');
const config = require('../../config');

// JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'yizhanbang-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// 微信配置
const WECHAT_APPID = process.env.WECHAT_APPID;
const WECHAT_SECRET = process.env.WECHAT_SECRET;

/**
 * 生成JWT令牌
 * @param {Object} user 用户对象
 * @returns {String} JWT令牌
 */
const generateToken = (user) => {
  console.log('====== 生成Token开始 ======');
  console.log('用户信息:', {
    id: user.id,
    phone: user.phone,
    isAdmin: user.isAdmin || false,
    roles: user.roles || []
  });
  
  const tokenPayload = { 
    id: user.id,
    phone: user.phone,
    isAdmin: user.isAdmin || false,
    roles: user.roles || []
  };
  
  console.log('Token载荷:', tokenPayload);
  console.log('JWT密钥:', config.jwt.secret);
  console.log('JWT过期时间:', config.jwt.expiresIn);
  
  const token = jwt.sign(
    tokenPayload,
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  );
  
  console.log('生成的Token:', token.substring(0, 20) + '...');
  console.log('====== 生成Token结束 ======');
  
  return token;
};

/**
 * 检查手机号是否已注册
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.checkPhone = async (req, res) => {
  try {
    const { phone } = req.query;
    
    console.log('检查手机号:', phone);
    
    if (!phone) {
      return res.status(400).json({
        code: 1,
        message: '手机号不能为空'
      });
    }
    
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      return res.status(400).json({
        code: 1,
        message: '手机号格式不正确'
      });
    }
    
    const user = await User.findOne({ where: { phone } });
    console.log('用户查询结果:', user ? '存在' : '不存在');
    
    return res.json({
      code: 0,
      message: '检查成功',
      data: {
        exists: !!user
      }
    });
  } catch (error) {
    console.error('检查手机号错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试'
    });
  }
};

/**
 * 发送短信验证码
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.sendSmsCode = async (req, res) => {
  // 调用smsController中的sendSmsCode方法
  return smsController.sendSmsCode(req, res);
};

/**
 * 微信登录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.wxLogin = async (req, res) => {
  try {
    console.log('收到微信登录请求:', req.body);
    const { code, userInfo } = req.body;
    
    if (!code) {
      console.log('微信登录失败: code不能为空');
      return res.status(400).json({
        code: 1,
        message: '微信登录code不能为空'
      });
    }
    
    console.log('微信登录配置:', { 
      appid: WECHAT_APPID, 
      secret: WECHAT_SECRET ? '已配置' : '未配置' 
    });
    
    // 通过code获取openid和session_key
    const wxApiUrl = `https://api.weixin.qq.com/sns/jscode2session?appid=${WECHAT_APPID}&secret=${WECHAT_SECRET}&js_code=${code}&grant_type=authorization_code`;
    console.log('请求微信API:', wxApiUrl);
    
    try {
      const wxRes = await axios.get(wxApiUrl);
      console.log('微信API返回:', wxRes.data);
      
      const { openid, session_key, unionid, errcode, errmsg } = wxRes.data;
      
      if (errcode) {
        console.log('微信API返回错误:', { errcode, errmsg });
        return res.status(400).json({
          code: errcode,
          message: `微信登录失败: ${errmsg}`
        });
      }
      
      if (!openid) {
        console.log('微信API返回错误: 没有openid');
        return res.status(400).json({
          code: 2,
          message: '微信登录失败: 无法获取openid'
        });
      }
      
      // 检查用户是否存在
      console.log('查询用户, openid:', openid);
      let user = await User.findOne({ where: { openid: openid } });
      let isNewUser = false;
      
      if (!user) {
        // 创建新用户
        console.log('用户不存在，创建新用户');
        isNewUser = true;
        
        const userData = {
          openid: openid,
          sessionKey: session_key,
          registerSource: 'wechat',
          registerIp: req.ip,
          lastLoginIp: req.ip,
          lastLoginTime: new Date(),
          loginCount: 1,
          realNameAuth: {
            isVerified: false
          }
        };
        
        // 如果有unionid，添加到用户数据中
        if (unionid) {
          userData.unionid = unionid;
        }
        
        // 如果有用户信息，添加到用户数据中
        if (userInfo) {
          userData.nickname = userInfo.nickName;
          userData.avatar = userInfo.avatarUrl;
          userData.gender = userInfo.gender;
        }
        
        console.log('创建用户数据:', userData);
        user = await User.create(userData);
        console.log('新用户创建成功, ID:', user.id);
      } else {
        // 更新现有用户
        console.log('用户已存在，更新用户信息, ID:', user.id);
        user.sessionKey = session_key;
        user.lastLoginIp = req.ip;
        user.lastLoginTime = new Date();
        user.loginCount = user.loginCount + 1;
        
        // 如果有unionid且用户未设置，更新用户的unionid
        if (unionid && !user.unionid) {
          user.unionid = unionid;
        }
        
        // 如果有用户信息且用户未设置，更新用户信息
        if (userInfo && (!user.nickname || !user.avatar)) {
          if (!user.nickname) user.nickname = userInfo.nickName;
          if (!user.avatar) user.avatar = userInfo.avatarUrl;
          if (user.gender === 0) user.gender = userInfo.gender;
        }
        
        await user.save();
        console.log('用户信息更新成功');
      }
      
      // 生成token
      const token = generateToken(user);
      console.log('生成token成功');
      
      // 返回用户信息和token
      const response = {
        code: 0,
        message: '登录成功',
        data: {
          token,
          user: {
            id: user.id,
            nickname: user.nickname,
            avatar: user.avatar,
            roles: user.roles,
            isNewUser
          }
        }
      };
      
      console.log('登录成功，返回数据:', response);
      return res.json(response);
    } catch (wxError) {
      console.error('调用微信API出错:', wxError);
      return res.status(500).json({
        code: 500,
        message: `调用微信API出错: ${wxError.message}`,
        error: process.env.NODE_ENV === 'development' ? wxError.toString() : undefined
      });
    }
  } catch (error) {
    console.error('微信登录处理出错:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试',
      error: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
};

/**
 * 手机号码登录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.phoneLogin = async (req, res) => {
  try {
    const { phone, code } = req.body;
    
    if (!phone || !code) {
      return res.status(400).json({
        code: 1,
        message: '手机号和验证码不能为空'
      });
    }
    
    // 验证短信验证码
    const verifyResult = smsController.verifyCode(phone, code, 'login');
    if (!verifyResult.valid) {
      return res.status(400).json({
        code: 2,
        message: verifyResult.message
      });
    }
    
    // 检查用户是否存在
    let user = await User.findOne({ where: { phone } });
    let isNewUser = false;
    
    if (!user) {
      // 创建新用户
      isNewUser = true;
      
      user = await User.create({
        phone,
        registerSource: 'phone',
        registerIp: req.ip,
        lastLoginIp: req.ip,
        lastLoginTime: new Date(),
        loginCount: 1
      });
    } else {
      // 更新现有用户
      user.lastLoginIp = req.ip;
      user.lastLoginTime = new Date();
      user.loginCount = user.loginCount + 1;
      
      await user.save();
    }
    
    // 生成token
    const token = generateToken(user);
    
    // 返回用户信息和token
    return res.json({
      code: 0,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          roles: user.roles,
          isNewUser
        }
      }
    });
  } catch (error) {
    console.error('手机登录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试'
    });
  }
};

/**
 * 用户登录（密码方式）
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.login = async (req, res) => {
  try {
    console.log('====== 普通用户登录请求开始 ======');
    console.log('登录数据:', req.body);
    
    const { phone, password } = req.body;

    // 检查用户是否存在，使用withPassword作用域获取密码字段
    const user = await User.scope('withPassword').findOne({ 
      where: { 
        phone,
        isAdmin: false // 确保只查询普通用户，不包括管理员
      } 
    });
    
    if (!user) {
      console.log('登录失败: 用户不存在或为管理员账户 -', phone);
      return res.status(401).json({
        code: 401,
        message: '手机号或密码错误'
      });
    }

    console.log('找到普通用户:', user.id, user.phone, '角色:', user.roles);

    // 验证密码
    if (!user.password) {
      console.log('登录失败: 用户未设置密码');
      return res.status(401).json({
        code: 401,
        message: '用户未设置密码，请使用其他登录方式'
      });
    }

    // 使用用户模型的comparePassword方法
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      console.log('登录失败: 密码错误');
      return res.status(401).json({
        code: 401,
        message: '手机号或密码错误'
      });
    }

    console.log('密码验证成功');

    // 更新登录信息
    user.lastLoginIp = req.ip;
    user.lastLoginTime = new Date();
    user.loginCount = (user.loginCount || 0) + 1;
    await user.save();

    // 生成token
    const token = generateToken(user);

    // 返回用户信息和token
    console.log('====== 普通用户登录成功 ======');
    res.json({
      code: 0,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          avatar: user.avatar,
          roles: user.roles,
          createdAt: user.createdAt
        }
      }
    });
  } catch (error) {
    console.error('登录异常:', error);
    res.status(500).json({
      code: 500,
      message: '登录失败',
      error: error.message
    });
  }
};

/**
 * 用户注册
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.register = async (req, res) => {
  try {
    const { phone, password, nickname, avatar } = req.body;

    // 检查手机号是否已存在
    const existingUser = await User.findOne({ where: { phone } });
    if (existingUser) {
      return res.status(400).json({
        code: 400,
        message: '该手机号已被注册'
      });
    }

    // 创建新用户
    const user = await User.create({
      phone,
      password: await bcrypt.hash(password, 10),
      nickname: nickname || `用户${phone.substr(-4)}`,
      avatar: avatar || null
    });

    // 生成token
    const token = generateToken(user);

    // 返回用户信息和token
    res.status(201).json({
      code: 0,
      message: '注册成功',
      data: {
        token,
        user: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          avatar: user.avatar,
          createdAt: user.createdAt
        }
      }
    });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      code: 500,
      message: '注册失败',
      error: error.message
    });
  }
};

/**
 * 重置密码
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.resetPassword = async (req, res) => {
  try {
    const { phone, newPassword } = req.body;

    // 查找用户
    const user = await User.findOne({ where: { phone } });
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    // 更新密码
    user.password = await bcrypt.hash(newPassword, 10);
    await user.save();

    res.json({
      code: 0,
      message: '密码重置成功'
    });
  } catch (error) {
    console.error('重置密码错误:', error);
    res.status(500).json({
      code: 500,
      message: '重置密码失败',
      error: error.message
    });
  }
};

/**
 * 用户登出
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.logout = async (req, res) => {
  try {
    // 客户端应该删除token，服务端无需特殊处理
    res.json({
      code: 0,
      message: '登出成功'
    });
  } catch (error) {
    console.error('登出错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

/**
 * 刷新Token
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.refreshToken = async (req, res) => {
  try {
    // 用户信息已在auth中间件中添加到req.user
    const user = req.user;
    
    // 生成新token
    const token = generateToken(user);
    
    res.json({
      code: 0,
      message: 'Token刷新成功',
      data: {
        token
      }
    });
  } catch (error) {
    console.error('刷新Token错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

/**
 * 获取登录状态
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getLoginStatus = async (req, res) => {
  try {
    // 用户信息已在auth中间件中添加到req.user
    const user = req.user;

    res.json({
      code: 0,
      message: '已登录',
      data: {
        user: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          avatar: user.avatar,
          createdAt: user.createdAt
        }
      }
    });
  } catch (error) {
    console.error('验证登录状态错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

/**
 * 更新用户信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateProfile = async (req, res) => {
  try {
    const { nickname, avatar } = req.body;
    const userId = req.user.id;

    // 查找用户
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    // 更新用户信息
    if (nickname) user.nickname = nickname;
    if (avatar) user.avatar = avatar;
    await user.save();

    res.json({
      code: 0,
      message: '更新成功',
      data: {
        user: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          avatar: user.avatar,
          createdAt: user.createdAt
        }
      }
    });
  } catch (error) {
    console.error('更新用户信息错误:', error);
    res.status(500).json({
      code: 500,
      message: '更新失败',
      error: error.message
    });
  }
}; 