const axios = require('axios');
const config = require('../config/config');
const User = require('../models/User');

// 缓存验证码，生产环境建议使用Redis等持久化存储
const codeCache = {};

// 生成随机验证码
const generateRandomCode = (length = 6) => {
  let code = '';
  for (let i = 0; i < length; i++) {
    code += Math.floor(Math.random() * 10);
  }
  return code;
};

// 检查手机号格式
const isValidPhone = (phone) => {
  return /^1[3-9]\d{9}$/.test(phone);
};

// 检查发送频率限制
const checkSendLimit = (phone) => {
  const now = Date.now();
  const codeInfo = codeCache[phone];
  
  if (codeInfo) {
    // 60秒内不能重复发送
    if (now - codeInfo.sendTime < 60000) {
      return {
        canSend: false,
        message: '发送过于频繁，请稍后再试',
        waitTime: Math.ceil((60000 - (now - codeInfo.sendTime)) / 1000)
      };
    }
    
    // 检查1小时内发送次数
    const oneHourAgo = now - 3600000;
    if (codeInfo.sendTime > oneHourAgo && codeInfo.sendCount >= 5) {
      return {
        canSend: false,
        message: '发送次数过多，请稍后再试',
        waitTime: Math.ceil((codeInfo.sendTime + 3600000 - now) / 1000)
      };
    }
  }
  
  return { canSend: true };
};

// 发送短信验证码
exports.sendSmsCode = async (req, res) => {
  try {
    console.log('收到发送短信验证码请求:', req.body);
    const { phone, type } = req.body;
    
    // 验证参数
    if (!phone || !type) {
      console.log('参数不完整:', { phone, type });
      return res.status(400).json({
        code: 1,
        message: '参数不完整'
      });
    }
    
    // 验证手机号格式
    if (!isValidPhone(phone)) {
      console.log('手机号格式不正确:', phone);
      return res.status(400).json({
        code: 1,
        message: '手机号格式不正确'
      });
    }
    
    // 验证短信类型
    if (!['login', 'register', 'resetPassword'].includes(type)) {
      console.log('短信类型不正确:', type);
      return res.status(400).json({
        code: 1,
        message: '短信类型不正确'
      });
    }
    
    // 检查发送频率限制
    const limitCheck = checkSendLimit(phone);
    if (!limitCheck.canSend) {
      console.log('发送频率限制:', limitCheck);
      return res.status(429).json({
        code: 1,
        message: limitCheck.message,
        waitTime: limitCheck.waitTime
      });
    }
    
    console.log('开始查询用户:', phone);
    
    // 如果是注册验证码，先检查手机号是否已注册
    if (type === 'register') {
      try {
        console.log('查询用户是否已注册');
        const existingUser = await User.findOne({ where: { phone } });
        console.log('查询结果:', existingUser);
        if (existingUser) {
          return res.status(400).json({
            code: 1,
            message: '该手机号已注册'
          });
        }
      } catch (err) {
        console.error('查询用户失败:', err);
        throw err;
      }
    }
    
    // 如果是重置密码或登录验证码，检查手机号是否已注册
    if (type === 'resetPassword' || type === 'login') {
      try {
        console.log('查询用户是否存在');
        const existingUser = await User.findOne({ where: { phone } });
        console.log('查询结果:', existingUser);
        if (!existingUser) {
          return res.status(400).json({
            code: 1,
            message: '该手机号未注册'
          });
        }
      } catch (err) {
        console.error('查询用户失败:', err);
        throw err;
      }
    }
    
    // 生成6位随机验证码
    const code = generateRandomCode(6);
    
    // 设置不同类型的短信模板ID
    let tplId;
    switch (type) {
      case 'login':
        tplId = config.sms.loginTemplateId;
        break;
      case 'register':
        tplId = config.sms.registerTemplateId;
        break;
      case 'resetPassword':
        tplId = config.sms.resetPasswordTemplateId;
        break;
      default:
        tplId = config.sms.loginTemplateId;
    }
    
    // 准备短信发送参数
    const smsParams = {
      key: config.sms.apiKey,
      mobile: phone,
      tpl_id: tplId,
      vars: JSON.stringify({
        code: code
      })
    };
    
    // 实际环境下发送短信
    if (config.environment === 'production') {
      try {
        const response = await axios.post('http://v.juhe.cn/sms/send', null, {
          params: smsParams,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        });
        
        if (response.data.error_code !== 0) {
          console.error('短信发送失败:', response.data);
          return res.status(500).json({
            code: 1,
            message: `短信发送失败: ${response.data.reason || '未知错误'}`
          });
        }
      } catch (error) {
        console.error('短信API调用失败:', error);
        return res.status(500).json({
          code: 1,
          message: '短信服务异常，请稍后再试'
        });
      }
    } else {
      // 开发环境下不实际发送短信，只打印验证码
      console.log(`模拟发送短信验证码到 ${phone}: 验证码为 ${code}`);
    }
    
    // 保存验证码到缓存
    const now = Date.now();
    if (!codeCache[phone]) {
      codeCache[phone] = {
        code,
        sendTime: now,
        expireTime: now + 10 * 60 * 1000, // 10分钟有效期
        sendCount: 1,
        type
      };
    } else {
      // 更新已有记录
      codeCache[phone] = {
        code,
        sendTime: now,
        expireTime: now + 10 * 60 * 1000,
        sendCount: (codeCache[phone].sendTime > now - 3600000) 
          ? codeCache[phone].sendCount + 1 
          : 1,
        type
      };
    }
    
    // 设置定时清理过期验证码
    setTimeout(() => {
      if (codeCache[phone] && codeCache[phone].code === code) {
        delete codeCache[phone];
      }
    }, 10 * 60 * 1000);
    
    return res.json({
      code: 0,
      message: '验证码发送成功',
      data: {
        expireTime: 10 * 60 // 10分钟有效期（秒）
      }
    });
  } catch (error) {
    console.error('发送验证码失败:', error);
    return res.status(500).json({
      code: 1,
      message: '服务器错误，请稍后再试'
    });
  }
};

// 校验验证码
exports.verifyCode = (phone, code, type) => {
  const codeInfo = codeCache[phone];
  
  // 验证码不存在
  if (!codeInfo) {
    return {
      valid: false,
      message: '验证码不存在或已过期'
    };
  }
  
  // 验证码类型不匹配
  if (codeInfo.type !== type) {
    return {
      valid: false,
      message: '验证码类型不匹配'
    };
  }
  
  // 验证码已过期
  if (Date.now() > codeInfo.expireTime) {
    delete codeCache[phone];
    return {
      valid: false,
      message: '验证码已过期'
    };
  }
  
  // 验证码不匹配
  if (codeInfo.code !== code) {
    return {
      valid: false,
      message: '验证码错误'
    };
  }
  
  // 验证通过后删除验证码，防止重复使用
  delete codeCache[phone];
  
  return {
    valid: true,
    message: '验证码正确'
  };
};

// 获取验证码接口(仅开发环境使用)
exports.getCodeForDev = (req, res) => {
  if (config.environment !== 'development') {
    return res.status(403).json({
      code: 1,
      message: '该接口仅在开发环境可用'
    });
  }
  
  const { phone } = req.query;
  
  if (!phone || !codeCache[phone]) {
    return res.status(404).json({
      code: 1,
      message: '未找到该手机号的验证码'
    });
  }
  
  return res.json({
    code: 0,
    message: '获取成功',
    data: {
      code: codeCache[phone].code,
      expireTime: Math.floor((codeCache[phone].expireTime - Date.now()) / 1000)
    }
  });
}; 