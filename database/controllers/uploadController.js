const path = require('path');
const fs = require('fs');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');

// 确保上传目录存在
const createUploadDir = (dir) => {
  const uploadDir = path.join(__dirname, '../../uploads', dir);
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  return uploadDir;
};

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const type = req.query.type || 'common';
    const uploadDir = createUploadDir(type);
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = uuidv4();
    const fileExt = path.extname(file.originalname);
    cb(null, `${uniqueSuffix}${fileExt}`);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 只接受图片文件
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('只能上传图片文件'), false);
  }
};

// 创建上传中间件
const upload = multer({ 
  storage, 
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 限制5MB
  }
});

/**
 * 上传验证图片
 */
exports.uploadVerificationImage = (req, res) => {
  const uploadSingle = upload.single('file');

  uploadSingle(req, res, function (err) {
    if (err) {
      return res.status(400).json({
        code: 400,
        message: err.message || '上传失败'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '没有选择文件'
      });
    }

    // 构建文件URL
    const type = req.query.type || 'common';
    const fileName = req.file.filename;
    const fileUrl = `/uploads/${type}/${fileName}`;

    res.json({
      code: 0,
      message: '上传成功',
      data: {
        url: fileUrl,
        filename: fileName,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      }
    });
  });
};

/**
 * 删除上传的图片
 */
exports.deleteUploadedFile = (req, res) => {
  const { filename, type = 'common' } = req.body;

  if (!filename) {
    return res.status(400).json({
      code: 400,
      message: '文件名不能为空'
    });
  }

  const filePath = path.join(__dirname, '../../uploads', type, filename);

  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({
      code: 404,
      message: '文件不存在'
    });
  }

  // 删除文件
  try {
    fs.unlinkSync(filePath);
    res.json({
      code: 0,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除文件失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除文件失败',
      error: error.message
    });
  }
}; 