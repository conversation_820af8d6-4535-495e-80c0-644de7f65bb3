const { Op } = require('sequelize');
const { StationVerification, User, Station } = require('../models');
const { sequelize } = require('../sequelize');

/**
 * 提交驿站认证申请
 */
exports.submitVerification = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    // 检查用户是否已登录
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        message: '请先登录后再提交认证申请'
      });
    }
    
    const userId = req.user.id;
    const {
      stationName,
      stationCode,
      stationType,
      brand,
      address,
      longitude,
      latitude,
      contactName,
      contactPhone,
      businessLicense,
      authorization,
      storefront,
      interior
    } = req.body;

    // 检查必填字段
    if (!stationName || !stationCode || !stationType || !brand || !address || 
        !contactName || !contactPhone || !businessLicense || !storefront || !interior) {
      return res.status(400).json({
        code: 400,
        message: '请填写所有必填字段'
      });
    }

    // 检查是否已经提交过认证申请
    const existingVerification = await StationVerification.findOne({
      where: { userId },
      order: [['createdAt', 'DESC']]
    });

    if (existingVerification && existingVerification.status === 'pending') {
      return res.status(400).json({
        code: 400,
        message: '您已提交认证申请，请等待审核'
      });
    }

    // 创建新的认证申请
    const verification = await StationVerification.create({
      userId,
      stationName,
      stationCode,
      stationType,
      brand,
      address,
      longitude,
      latitude,
      contactName,
      contactPhone,
      businessLicense,
      authorization,
      storefront,
      interior,
      status: 'pending'
    }, { transaction });

    // 更新用户的认证状态
    await User.update(
      { isStationVerifying: true },
      { where: { id: userId }, transaction }
    );

    await transaction.commit();

    res.json({
      code: 0,
      message: '驿站认证申请提交成功',
      data: verification
    });
  } catch (error) {
    await transaction.rollback();
    console.error('提交驿站认证申请失败:', error);
    res.status(500).json({
      code: 500,
      message: '提交驿站认证申请失败',
      error: error.message
    });
  }
};

/**
 * 获取驿站认证状态
 */
exports.getVerificationStatus = async (req, res) => {
  try {
    // 检查用户是否已登录
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        message: '请先登录后再查询认证状态'
      });
    }
    
    const userId = req.user.id;

    // 查询最近的认证申请
    const verification = await StationVerification.findOne({
      where: { userId },
      order: [['createdAt', 'DESC']]
    });

    if (!verification) {
      return res.json({
        code: 0,
        message: '未提交过认证申请',
        data: {
          status: null
        }
      });
    }

    res.json({
      code: 0,
      message: '获取认证状态成功',
      data: {
        status: verification.status,
        remark: verification.remark,
        verification
      }
    });
  } catch (error) {
    console.error('获取驿站认证状态失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取驿站认证状态失败',
      error: error.message
    });
  }
};

/**
 * 重新提交驿站认证申请
 */
exports.resubmitVerification = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    // 检查用户是否已登录
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        message: '请先登录后再重新提交认证申请'
      });
    }
    
    const userId = req.user.id;
    const {
      stationName,
      stationCode,
      stationType,
      brand,
      address,
      longitude,
      latitude,
      contactName,
      contactPhone,
      businessLicense,
      authorization,
      storefront,
      interior
    } = req.body;

    // 检查必填字段
    if (!stationName || !stationCode || !stationType || !brand || !address || 
        !contactName || !contactPhone || !businessLicense || !storefront || !interior) {
      return res.status(400).json({
        code: 400,
        message: '请填写所有必填字段'
      });
    }

    // 检查是否有被拒绝的认证申请
    const existingVerification = await StationVerification.findOne({
      where: { userId },
      order: [['createdAt', 'DESC']]
    });

    if (!existingVerification || existingVerification.status !== 'rejected') {
      return res.status(400).json({
        code: 400,
        message: '没有被拒绝的认证申请可重新提交'
      });
    }

    // 创建新的认证申请
    const verification = await StationVerification.create({
      userId,
      stationName,
      stationCode,
      stationType,
      brand,
      address,
      longitude,
      latitude,
      contactName,
      contactPhone,
      businessLicense,
      authorization,
      storefront,
      interior,
      status: 'pending'
    }, { transaction });

    // 更新用户的认证状态
    await User.update(
      { isStationVerifying: true },
      { where: { id: userId }, transaction }
    );

    await transaction.commit();

    res.json({
      code: 0,
      message: '驿站认证申请重新提交成功',
      data: verification
    });
  } catch (error) {
    await transaction.rollback();
    console.error('重新提交驿站认证申请失败:', error);
    res.status(500).json({
      code: 500,
      message: '重新提交驿站认证申请失败',
      error: error.message
    });
  }
};

/**
 * 审核驿站认证申请
 */
exports.reviewVerification = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    // 检查用户是否已登录
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        message: '请先登录后再进行审核操作'
      });
    }
    
    const { id } = req.params;
    const { status, remark } = req.body;
    const adminId = req.user.id;

    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        code: 400,
        message: '无效的审核状态'
      });
    }

    const verification = await StationVerification.findByPk(id);
    if (!verification) {
      return res.status(404).json({
        code: 404,
        message: '认证申请不存在'
      });
    }

    if (verification.status !== 'pending') {
      return res.status(400).json({
        code: 400,
        message: '该申请已审核'
      });
    }

    // 更新认证申请状态
    await verification.update({
      status,
      remark,
      reviewedBy: adminId,
      reviewedAt: new Date()
    }, { transaction });

    // 更新用户状态
    if (status === 'approved') {
      // 创建新的驿站
      await Station.create({
        name: verification.stationName,
        code: verification.stationCode,
        type: verification.stationType,
        ownerId: verification.userId,
        address: verification.address,
        province: '', // 需要从地址中解析
        city: '',
        district: '',
        longitude: verification.longitude,
        latitude: verification.latitude,
        phone: verification.contactPhone,
        images: {
          storefront: verification.storefront,
          interior: verification.interior,
          businessLicense: verification.businessLicense,
          authorization: verification.authorization
        },
        status: 'approved',
        verifiedAt: new Date()
      }, { transaction });

      // 更新用户为已认证状态
      await User.update(
        { 
          isStationVerified: true,
          isStationVerifying: false,
          roles: sequelize.literal(`JSON_ARRAY_APPEND(roles, '$', 'station_owner')`)
        },
        { where: { id: verification.userId }, transaction }
      );
    } else {
      // 拒绝认证，更新用户状态
      await User.update(
        { isStationVerifying: false },
        { where: { id: verification.userId }, transaction }
      );
    }

    await transaction.commit();

    res.json({
      code: 0,
      message: `认证申请${status === 'approved' ? '通过' : '拒绝'}成功`,
      data: verification
    });
  } catch (error) {
    await transaction.rollback();
    console.error('审核驿站认证申请失败:', error);
    res.status(500).json({
      code: 500,
      message: '审核驿站认证申请失败',
      error: error.message
    });
  }
};

/**
 * 获取驿站认证申请列表
 */
exports.getVerificationList = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      keyword
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    if (status) {
      where.status = status;
    }

    if (keyword) {
      where[Op.or] = [
        { stationName: { [Op.like]: `%${keyword}%` } },
        { stationCode: { [Op.like]: `%${keyword}%` } },
        { contactName: { [Op.like]: `%${keyword}%` } },
        { contactPhone: { [Op.like]: `%${keyword}%` } }
      ];
    }

    const { count, rows } = await StationVerification.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'phone', 'avatar'],
          required: false
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'nickname'],
          required: false
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // 格式化数据，添加中文状态描述
    const formattedRows = rows.map(row => {
      const item = row.toJSON();
      item.statusText = getStatusText(item.status);
      return item;
    });

    res.json({
      code: 0,
      message: '获取认证申请列表成功',
      data: {
        list: formattedRows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        statusCount: {
          pending: await StationVerification.count({ where: { status: 'pending' } }),
          approved: await StationVerification.count({ where: { status: 'approved' } }),
          rejected: await StationVerification.count({ where: { status: 'rejected' } }),
          total: count
        }
      }
    });
  } catch (error) {
    console.error('获取驿站认证申请列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取驿站认证申请列表失败',
      error: error.message
    });
  }
};

/**
 * 获取驿站认证申请详情
 */
exports.getVerificationDetail = async (req, res) => {
  try {
    const { id } = req.params;

    const verification = await StationVerification.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'phone', 'avatar']
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'nickname'],
          required: false
        }
      ]
    });

    if (!verification) {
      return res.status(404).json({
        code: 404,
        message: '认证申请不存在'
      });
    }

    // 格式化数据，添加中文状态描述
    const result = verification.toJSON();
    result.statusText = getStatusText(result.status);
    result.stationTypeText = getStationTypeText(result.stationType);

    res.json({
      code: 0,
      message: '获取认证申请详情成功',
      data: result
    });
  } catch (error) {
    console.error('获取驿站认证申请详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取驿站认证申请详情失败',
      error: error.message
    });
  }
};

/**
 * 获取状态文本
 */
function getStatusText(status) {
  const statusMap = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝'
  };
  return statusMap[status] || status;
}

/**
 * 获取驿站类型文本
 */
function getStationTypeText(type) {
  const typeMap = {
    'community': '社区驿站',
    'express': '快递驿站',
    'campus': '校园驿站',
    'office': '写字楼驿站',
    'commercial': '商业驿站'
  };
  return typeMap[type] || type;
} 