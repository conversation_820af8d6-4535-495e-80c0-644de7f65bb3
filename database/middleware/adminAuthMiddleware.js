const jwt = require('jsonwebtoken');
const User = require('../models/User');
const config = require('../../config');

/**
 * 验证管理员token
 */
const verifyAdminToken = async (req, res, next) => {
  try {
    // 从请求头或查询参数获取token
    const token = req.headers.authorization?.split(' ')[1] || req.query.token;
    
    console.log('========== 开始验证管理员Token ==========');
    console.log('请求路径:', req.path);
    console.log('Authorization头:', req.headers.authorization);
    console.log('提取的Token:', token ? token.substring(0, 15) + '...' : 'null');

    if (!token) {
      console.log('管理员Token验证失败: 未提供token');
      return res.status(401).json({
        code: 401,
        message: '未提供身份验证令牌'
      });
    }

    try {
      // 验证token
      console.log('JWT验证，使用密钥:', config.jwt.secret);
      const decoded = jwt.verify(token, config.jwt.secret);
      
      console.log('Token解析结果:', decoded);
      
      // 确保是管理员token
      if (!decoded.isAdmin && (!decoded.roles || !decoded.roles.includes('admin'))) {
        console.log('Token不是管理员token');
        return res.status(403).json({
          code: 403,
          message: '需要管理员权限'
        });
      }
      
      // 查询用户
      const admin = await User.findOne({
        where: {
          id: decoded.id,
          isAdmin: true
        }
      });
      
      if (!admin) {
        console.log('Token对应的管理员不存在:', decoded.id);
        return res.status(401).json({
          code: 401,
          message: '管理员不存在'
        });
      }
      
      // 确保用户确实是管理员
      if (!admin.isAdmin) {
        console.log('用户不是管理员:', decoded.id);
        return res.status(403).json({
          code: 403,
          message: '需要管理员权限'
        });
      }
      
      console.log('管理员验证成功:', admin.id, admin.phone, '角色:', admin.roles);

      // 将管理员信息添加到请求对象
      req.user = admin;
      console.log('========== 管理员Token验证成功 ==========');
      next();
    } catch (jwtError) {
      console.error('JWT验证失败:', jwtError);
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(401).json({
          code: 401,
          message: '身份验证令牌已过期'
        });
      } else if (jwtError.name === 'JsonWebTokenError') {
        return res.status(401).json({
          code: 401,
          message: '无效的身份验证令牌',
          error: jwtError.message
        });
      }
      
      return res.status(401).json({
        code: 401,
        message: '无效的身份验证令牌'
      });
    }
  } catch (error) {
    console.error('Token验证未知错误:', error);
    return res.status(401).json({
      code: 401,
      message: '无效的身份验证令牌'
    });
  }
};

/**
 * 确保用户是管理员
 */
const ensureAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      code: 401,
      message: '需要身份验证'
    });
  }
  
  if (!req.user.isAdmin && (!req.user.roles || !req.user.roles.includes('admin'))) {
    return res.status(403).json({
      code: 403,
      message: '需要管理员权限'
    });
  }
  
  next();
};

/**
 * 管理员超级权限验证 - 仅超级管理员可访问
 */
const superAdminOnly = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      code: 401,
      message: '需要身份验证'
    });
  }
  
  // 检查是否具有超级管理员角色
  if (!req.user.roles || !req.user.roles.includes('superadmin')) {
    return res.status(403).json({
      code: 403,
      message: '需要超级管理员权限'
    });
  }
  
  next();
};

module.exports = {
  verifyAdminToken,
  ensureAdmin,
  superAdminOnly
}; 