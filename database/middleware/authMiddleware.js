const jwt = require('jsonwebtoken');
const User = require('../models/User');
const config = require('../../config');

/**
 * 保护需要登录的路由
 */
exports.protect = async (req, res, next) => {
  try {
    // 获取token
    let token;
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    // 检查token是否存在
    if (!token) {
      return res.status(401).json({
        code: 401,
        message: '请先登录'
      });
    }

    // 验证token
    try {
      const decoded = jwt.verify(token, config.jwt.secret);
      
      // 检查用户是否存在
      const user = await User.findByPk(decoded.id);
      
      if (!user) {
        return res.status(401).json({
          code: 401,
          message: '用户不存在，请重新登录'
        });
      }
      
      // 将用户信息添加到请求对象
      req.user = user;
      next();
    } catch (error) {
      return res.status(401).json({
        code: 401,
        message: '登录已过期，请重新登录',
        error: error.message
      });
    }
  } catch (error) {
    console.error('认证中间件错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

/**
 * 限制特定角色访问
 * @param {...string} roles 允许访问的角色
 */
exports.restrictTo = (...allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        message: '请先登录'
      });
    }

    // 检查用户角色是否包含允许的角色
    const userRoles = req.user.roles || ['user'];
    const hasPermission = allowedRoles.some(role => userRoles.includes(role));

    if (!hasPermission) {
      return res.status(403).json({
        code: 403,
        message: '您没有权限执行此操作'
      });
    }

    next();
  };
};

/**
 * 身份验证中间件
 */
const verifyToken = async (req, res, next) => {
  try {
    // 从请求头或查询参数获取token
    const token = req.headers.authorization?.split(' ')[1] || req.query.token;
    
    console.log('========== 开始验证Token ==========');
    console.log('请求路径:', req.path);
    console.log('完整请求头:', req.headers);
    console.log('Authorization头:', req.headers.authorization);
    console.log('提取的Token:', token ? token.substring(0, 15) + '...' : 'null');

    if (!token) {
      console.log('Token验证失败: 未提供token');
      return res.status(401).json({
        code: 401,
        message: '未提供身份验证令牌'
      });
    }

    try {
      // 验证token
      console.log('JWT验证，使用密钥:', config.jwt.secret);
      const decoded = jwt.verify(token, config.jwt.secret);
      
      console.log('Token解析结果:', decoded);
      
      // 查询用户
      const user = await User.findByPk(decoded.id);
      
      if (!user) {
        console.log('Token对应的用户不存在:', decoded.id);
        return res.status(401).json({
          code: 401,
          message: '用户不存在'
        });
      }
      
      console.log('用户验证成功:', user.id, user.phone, '管理员:', user.isAdmin, '角色:', user.roles);

      // 将用户信息添加到请求对象
      req.user = user;
      console.log('========== Token验证成功 ==========');
      next();
    } catch (jwtError) {
      console.error('JWT验证失败:', jwtError);
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(401).json({
          code: 401,
          message: '身份验证令牌已过期'
        });
      } else if (jwtError.name === 'JsonWebTokenError') {
        return res.status(401).json({
          code: 401,
          message: '无效的身份验证令牌',
          error: jwtError.message
        });
      }
      
      return res.status(401).json({
        code: 401,
        message: '无效的身份验证令牌'
      });
    }
  } catch (error) {
    console.error('Token验证未知错误:', error);
    return res.status(401).json({
      code: 401,
      message: '无效的身份验证令牌'
    });
  }
};

/**
 * 可选的JWT验证
 * 如果提供了有效的token，会将用户信息添加到req.user
 * 如果没有提供token或token无效，不会阻止请求继续
 */
const optionalAuth = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1] || req.query.token;
    
    if (!token) {
      return next();
    }

    const decoded = jwt.verify(token, config.jwt.secret);
    const user = await User.findByPk(decoded.id);
    
    if (user) {
      req.user = user;
    }
  } catch (error) {
    // 忽略错误，继续处理请求
  }
  
  next();
};

/**
 * 检查用户是否为管理员
 */
const isAdmin = (req, res, next) => {
  console.log('验证管理员权限:', req.user?.id, '是否为管理员:', req.user?.isAdmin);
  console.log('用户角色:', req.user?.roles);
  
  if (!req.user) {
    return res.status(401).json({
      code: 401,
      message: '需要身份验证，用户未登录'
    });
  }
  
  if (!req.user.isAdmin) {
    // 检查roles字段是否包含admin
    const hasAdminRole = req.user.roles && Array.isArray(req.user.roles) && req.user.roles.includes('admin');
    
    if (hasAdminRole) {
      // 如果roles中包含admin但isAdmin为false，自动更新isAdmin
      console.log('用户拥有admin角色但isAdmin为false，自动设置为管理员');
      req.user.isAdmin = true;
      // 继续执行下一个中间件
      return next();
    }
    
    return res.status(403).json({
      code: 403,
      message: '需要管理员权限'
    });
  }
  
  next();
};

module.exports = {
  verifyToken,
  optionalAuth,
  isAdmin
}; 