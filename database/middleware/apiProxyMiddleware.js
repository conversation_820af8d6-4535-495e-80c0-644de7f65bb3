/**
 * API代理中间件
 * 用于处理后台管理系统和小程序之间的数据交互
 */
const { User, Station, Order, StationVerification } = require('../models');
const { Op } = require('sequelize');

/**
 * 小程序API代理中间件
 * 处理后台管理系统对小程序数据的访问
 */
const miniprogramApiProxy = async (req, res) => {
  try {
    const { module, method } = req.params;
    const params = req.body || {};
    
    console.log(`接收到小程序API代理请求: ${module}.${method}`, params);
    
    let result = null;
    
    // 根据模块和方法分发请求
    switch (module) {
      case 'user':
        result = await handleUserModule(method, params);
        break;
      case 'station':
        result = await handleStationModule(method, params);
        break;
      case 'order':
        result = await handleOrderModule(method, params);
        break;
      case 'verification':
        result = await handleVerificationModule(method, params);
        break;
      default:
        return res.status(400).json({
          code: 400,
          message: `未知模块: ${module}`
        });
    }
    
    // 返回结果
    res.json({
      code: 0,
      message: '操作成功',
      data: result
    });
  } catch (error) {
    console.error('小程序API代理错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

/**
 * 处理用户模块请求
 */
async function handleUserModule(method, params) {
  switch (method) {
    case 'getList':
      const { page = 1, limit = 10, keyword } = params;
      const offset = (page - 1) * limit;
      const where = {};
      
      if (keyword) {
        where[Op.or] = [
          { nickname: { [Op.like]: `%${keyword}%` } },
          { phone: { [Op.like]: `%${keyword}%` } }
        ];
      }
      
      const { count, rows } = await User.findAndCountAll({
        where,
        attributes: ['id', 'nickname', 'phone', 'avatar', 'gender', 'createdAt', 'lastLoginTime', 'loginCount'],
        order: [['createdAt', 'DESC']],
        offset,
        limit: parseInt(limit)
      });
      
      return {
        list: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit)
      };
      
    case 'getDetail':
      const { id } = params;
      const user = await User.findByPk(id, {
        attributes: ['id', 'nickname', 'phone', 'avatar', 'gender', 'createdAt', 'lastLoginTime', 'loginCount']
      });
      
      if (!user) {
        throw new Error('用户不存在');
      }
      
      return user;
      
    default:
      throw new Error(`未知方法: ${method}`);
  }
}

/**
 * 处理驿站模块请求
 */
async function handleStationModule(method, params) {
  switch (method) {
    case 'getList':
      const { page = 1, limit = 10, keyword, status } = params;
      const offset = (page - 1) * limit;
      const where = {};
      
      if (keyword) {
        where[Op.or] = [
          { name: { [Op.like]: `%${keyword}%` } },
          { code: { [Op.like]: `%${keyword}%` } },
          { address: { [Op.like]: `%${keyword}%` } }
        ];
      }
      
      if (status) {
        where.status = status;
      }
      
      const { count, rows } = await Station.findAndCountAll({
        where,
        include: [
          {
            model: User,
            as: 'owner',
            attributes: ['id', 'nickname', 'phone']
          }
        ],
        order: [['createdAt', 'DESC']],
        offset,
        limit: parseInt(limit)
      });
      
      return {
        list: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit)
      };
      
    case 'getDetail':
      const { id } = params;
      const station = await Station.findByPk(id, {
        include: [
          {
            model: User,
            as: 'owner',
            attributes: ['id', 'nickname', 'phone']
          }
        ]
      });
      
      if (!station) {
        throw new Error('驿站不存在');
      }
      
      return station;
      
    default:
      throw new Error(`未知方法: ${method}`);
  }
}

/**
 * 处理订单模块请求
 */
async function handleOrderModule(method, params) {
  switch (method) {
    case 'getList':
      const { page = 1, limit = 10, keyword, status } = params;
      const offset = (page - 1) * limit;
      const where = {};
      
      if (keyword) {
        where[Op.or] = [
          { orderNo: { [Op.like]: `%${keyword}%` } }
        ];
      }
      
      if (status) {
        where.status = status;
      }
      
      const { count, rows } = await Order.findAndCountAll({
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'phone']
          },
          {
            model: Station,
            as: 'station',
            attributes: ['id', 'name', 'address']
          }
        ],
        order: [['createdAt', 'DESC']],
        offset,
        limit: parseInt(limit)
      });
      
      return {
        list: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit)
      };
      
    case 'getDetail':
      const { id } = params;
      const order = await Order.findByPk(id, {
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'phone']
          },
          {
            model: Station,
            as: 'station',
            attributes: ['id', 'name', 'address']
          }
        ]
      });
      
      if (!order) {
        throw new Error('订单不存在');
      }
      
      return order;
      
    default:
      throw new Error(`未知方法: ${method}`);
  }
}

/**
 * 处理驿站认证模块请求
 */
async function handleVerificationModule(method, params) {
  switch (method) {
    case 'getList':
      const { page = 1, limit = 10, keyword, status } = params;
      const offset = (page - 1) * limit;
      const where = {};
      
      if (keyword) {
        where[Op.or] = [
          { stationName: { [Op.like]: `%${keyword}%` } },
          { stationCode: { [Op.like]: `%${keyword}%` } },
          { contactName: { [Op.like]: `%${keyword}%` } },
          { contactPhone: { [Op.like]: `%${keyword}%` } }
        ];
      }
      
      if (status) {
        where.status = status;
      }
      
      const { count, rows } = await StationVerification.findAndCountAll({
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'phone', 'avatar'],
            required: false
          },
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'nickname'],
            required: false
          }
        ],
        order: [['createdAt', 'DESC']],
        offset,
        limit: parseInt(limit)
      });
      
      // 格式化数据，添加中文状态描述
      const formattedRows = rows.map(row => {
        const item = row.toJSON();
        item.statusText = getStatusText(item.status);
        return item;
      });
      
      return {
        list: formattedRows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        statusCount: {
          pending: await StationVerification.count({ where: { status: 'pending' } }),
          approved: await StationVerification.count({ where: { status: 'approved' } }),
          rejected: await StationVerification.count({ where: { status: 'rejected' } }),
          total: count
        }
      };
      
    case 'getDetail':
      const { id } = params;
      const verification = await StationVerification.findByPk(id, {
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'phone', 'avatar'],
            required: false
          },
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'nickname'],
            required: false
          }
        ]
      });
      
      if (!verification) {
        throw new Error('认证申请不存在');
      }
      
      // 添加状态文本
      const result = verification.toJSON();
      result.statusText = getStatusText(result.status);
      
      return result;
      
    default:
      throw new Error(`未知方法: ${method}`);
  }
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
  const statusMap = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝'
  };
  return statusMap[status] || '未知';
}

module.exports = miniprogramApiProxy; 