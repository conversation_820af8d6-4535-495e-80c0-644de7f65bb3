// app.js
App({
  onLaunch() {
    console.log('App Launch')
    // 检查登录状态
    this.checkLoginStatus()
  },
  
  onShow() {
    console.log('App Show')
  },
  
  onHide() {
    console.log('App Hide')
  },
  
  methods: {
    // 检查登录状态
    async checkLoginStatus() {
      // 检查是否有token
      const token = wx.getStorageSync('token')
      if (!token) {
        console.log('未登录状态')
        return
      }
      
      try {
        // 可以添加token验证逻辑，如调用验证token的接口
        // 这里简化处理，假设有token就是已登录
        console.log('已登录状态')
      } catch (error) {
        console.error('登录状态检查失败', error)
        // 登录失效，清除token
        wx.removeStorageSync('token')
        wx.removeStorageSync('userInfo')
      }
    }
  },
  
  globalData: {
    userInfo: null,
    isLoggedIn: false
  }
})
