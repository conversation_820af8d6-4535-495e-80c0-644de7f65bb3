{"appid": "wxf20fc3e7fc1f8f5e", "compileType": "miniprogram", "libVersion": "3.8.3", "miniprogramRoot": "./", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "copyFiles": []}