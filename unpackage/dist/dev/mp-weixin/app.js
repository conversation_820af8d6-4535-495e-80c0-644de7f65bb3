// app.js
(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    ["app"], {
        "0": function(e, t, n) {
            "use strict";
            n.r(t);
            var o = n("1");
            o["default"].config.productionTip = !1;
            o["default"].prototype.$store = n("2")["default"];
            o["default"].prototype.$api = n("3");
            
            App({
                onLaunch: function() {
                    console.log("App Launch");
                    // 检查登录状态
                    this.checkLoginStatus();
                },
                onShow: function() {
                    console.log("App Show");
                },
                onHide: function() {
                    console.log("App Hide");
                },
                checkLoginStatus: function() {
                    // 检查是否有token
                    var token = wx.getStorageSync('token');
                    if (!token) {
                        console.log('未登录状态');
                        return;
                    }
                    
                    try {
                        // 简化处理，假设有token就是已登录
                        console.log('已登录状态');
                        this.globalData.isLoggedIn = true;
                        
                        // 获取用户信息
                        var userInfo = wx.getStorageSync('userInfo');
                        if (userInfo) {
                            try {
                                this.globalData.userInfo = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo;
                            } catch (e) {
                                console.error('解析用户信息失败:', e);
                            }
                        }
                    } catch (error) {
                        console.error('登录状态检查失败', error);
                        // 登录失效，清除token
                        wx.removeStorageSync('token');
                        wx.removeStorageSync('userInfo');
                        this.globalData.isLoggedIn = false;
                        this.globalData.userInfo = null;
                    }
                },
                globalData: {
                    userInfo: null,
                    isLoggedIn: false,
                    baseUrl: 'http://localhost:3000/api'
                }
            });
        }
    }
]);

require("./app.js");
