"use strict";
const common_vendor = require("../../common/vendor.js");
const api_request = require("../request.js");
function wxLogin() {
  return new Promise((resolve, reject) => {
    console.log("准备调用微信登录接口...");
    common_vendor.wx$1.login({
      timeout: 1e4,
      // 设置超时时间为10秒
      success: (res) => {
        console.log("微信登录接口返回:", res);
        if (res.code) {
          console.log("获取code成功，开始请求后端登录API...");
          let userInfo = common_vendor.wx$1.getStorageSync("wxUserInfo") || null;
          const requestData = {
            code: res.code,
            userInfo
          };
          console.log("发送登录请求数据:", requestData);
          api_request.request.post("/api/auth/wx-login", requestData).then((response) => {
            console.log("登录成功，服务器返回:", response);
            if (response.data && response.data.token) {
              common_vendor.wx$1.setStorageSync("token", response.data.token);
              common_vendor.wx$1.setStorageSync("userInfo", JSON.stringify(response.data.user || {}));
              if (response.data.isNewUser) {
                console.log("新用户登录，可能需要引导完善资料");
              }
            }
            resolve(response);
          }).catch((error) => {
            console.error("登录请求失败:", error);
            common_vendor.wx$1.showToast({
              title: error.message || "登录失败，请稍后再试",
              icon: "none",
              duration: 2e3
            });
            reject(error);
          });
        } else {
          console.error("获取code失败:", res.errMsg);
          common_vendor.wx$1.showToast({
            title: "微信登录失败: " + (res.errMsg || "未知错误"),
            icon: "none",
            duration: 2e3
          });
          reject(new Error("登录失败: " + (res.errMsg || "未知错误")));
        }
      },
      fail: (err) => {
        console.error("微信登录接口调用失败:", err);
        common_vendor.wx$1.showToast({
          title: "微信登录接口调用失败",
          icon: "none",
          duration: 2e3
        });
        reject(new Error("微信登录接口调用失败: " + (err.errMsg || "未知错误")));
      }
    });
  });
}
function checkLogin() {
  const token = common_vendor.wx$1.getStorageSync("token");
  return !!token;
}
function getUserInfo() {
  return common_vendor.wx$1.getStorageSync("userInfo") || {};
}
function logout() {
  common_vendor.wx$1.removeStorageSync("token");
  common_vendor.wx$1.removeStorageSync("userInfo");
  return Promise.resolve();
}
function loginByPhone(phone, password) {
  return api_request.request.post("/api/auth/login", {
    phone,
    password
  }).then((response) => {
    if (response.data && response.data.token) {
      common_vendor.wx$1.setStorageSync("token", response.data.token);
      common_vendor.wx$1.setStorageSync("userInfo", JSON.stringify(response.data.userInfo || {}));
    }
    return response;
  });
}
function loginByPhoneCode(phone, code) {
  return api_request.request.post("/api/auth/login-by-code", {
    phone,
    code
  }).then((response) => {
    if (response.data && response.data.token) {
      common_vendor.wx$1.setStorageSync("token", response.data.token);
      common_vendor.wx$1.setStorageSync("userInfo", JSON.stringify(response.data.userInfo || {}));
    }
    return response;
  });
}
function sendSmsCode(phone, type = "login") {
  return api_request.request.post("/api/auth/send-code", {
    phone,
    type
  });
}
function register(data) {
  return api_request.request.post("/api/auth/register", data);
}
const authApi = {
  wxLogin,
  checkLogin,
  getUserInfo,
  logout,
  loginByPhone,
  loginByPhoneCode,
  sendSmsCode,
  register
};
exports.authApi = authApi;
