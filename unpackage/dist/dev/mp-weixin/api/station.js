"use strict";
const api_request = require("./request.js");
function submitStationVerification(data) {
  return api_request.request.post("/api/station/verify", data);
}
function getStationVerificationStatus() {
  return api_request.request.get("/api/station/verify/status");
}
function uploadVerificationImage(filePath, type) {
  return api_request.request.upload("/api/upload/verification", filePath, "file", { type });
}
function resubmitStationVerification(data) {
  return api_request.request.put("/api/station/verify", data);
}
exports.getStationVerificationStatus = getStationVerificationStatus;
exports.resubmitStationVerification = resubmitStationVerification;
exports.submitStationVerification = submitStationVerification;
exports.uploadVerificationImage = uploadVerificationImage;
