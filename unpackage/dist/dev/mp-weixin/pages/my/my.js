"use strict";
const common_vendor = require("../../common/vendor.js");
const api_auth = require("../../api/auth.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      isLoggedIn: false,
      userInfo: {},
      // 常用功能列表
      functionList: [
        {
          id: "posts",
          name: "我的发布",
          icon: "/static/icons/my-posts.png",
          url: "/pages/my/posts",
          bgColor: "bg-blue",
          requireLogin: true
        },
        {
          id: "favorites",
          name: "我的收藏",
          icon: "/static/icons/favorites.png",
          url: "/pages/my/favorites",
          bgColor: "bg-yellow",
          requireLogin: true
        },
        {
          id: "history",
          name: "浏览历史",
          icon: "/static/icons/history.png",
          url: "/pages/my/history",
          bgColor: "bg-green",
          requireLogin: false
        },
        {
          id: "orders",
          name: "我的订单",
          icon: "/static/icons/orders.png",
          url: "/pages/my/orders",
          bgColor: "bg-purple",
          requireLogin: true
        },
        {
          id: "wallet",
          name: "我的钱包",
          icon: "/static/icons/wallet.png",
          url: "/pages/my/wallet",
          bgColor: "bg-pink",
          requireLogin: true
        },
        {
          id: "cart",
          name: "购物车",
          icon: "/static/icons/cart.png",
          url: "/pages/my/cart",
          bgColor: "bg-orange",
          requireLogin: true
        },
        {
          id: "promotion",
          name: "推广赚钱",
          icon: "/static/icons/promotion.png",
          url: "/pages/my/promotion",
          bgColor: "bg-yellow",
          requireLogin: true
        },
        {
          id: "verify-identity",
          name: "实名认证",
          icon: "/static/icons/verification.png",
          url: "/pages/my/verify-identity",
          bgColor: "bg-green",
          requireLogin: true
        },
        {
          id: "verify-station",
          name: "驿站认证",
          icon: "/static/icons/station-verification.png",
          url: "/pages/my/verify-station",
          bgColor: "bg-blue",
          requireLogin: true
        },
        {
          id: "business-settings",
          name: "商务合作",
          icon: "/static/icons/cooperation.png",
          url: "/pages/my/business-settings",
          bgColor: "bg-purple",
          requireLogin: true
        },
        {
          id: "service-orders",
          name: "顶班接单",
          icon: "/static/icons/service-orders.png",
          url: "/pages/my/service-orders",
          bgColor: "bg-yellow",
          requireLogin: true
        },
        {
          id: "settings",
          name: "设置",
          icon: "/static/icons/settings.png",
          url: "/pages/my/settings",
          bgColor: "bg-gray",
          requireLogin: false
        }
      ]
    };
  },
  onLoad() {
    this.checkLoginStatus();
  },
  onShow() {
    this.checkLoginStatus();
  },
  methods: {
    // 检查登录状态
    async checkLoginStatus() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        const userInfoStr = common_vendor.index.getStorageSync("userInfo");
        if (token && userInfoStr) {
          let userInfo;
          if (typeof userInfoStr === "string") {
            try {
              userInfo = JSON.parse(userInfoStr);
            } catch (e) {
              console.error("解析用户信息失败:", e);
              userInfo = userInfoStr;
            }
          } else {
            userInfo = userInfoStr;
          }
          this.userInfo = userInfo;
          this.isLoggedIn = true;
          await this.verifyToken();
        } else {
          this.isLoggedIn = false;
          this.userInfo = {};
        }
      } catch (err) {
        console.error("检查登录状态失败:", err);
        this.isLoggedIn = false;
        this.userInfo = {};
      }
    },
    // 验证 token 是否有效
    async verifyToken() {
      try {
        const result = await api_auth.getLoginStatus();
        if (result && result.code === 0) {
          if (result.data && result.data.user) {
            this.userInfo = result.data.user;
            common_vendor.index.setStorageSync("userInfo", JSON.stringify(result.data.user));
          }
        } else {
          common_vendor.index.removeStorageSync("token");
          common_vendor.index.removeStorageSync("userInfo");
          this.isLoggedIn = false;
          this.userInfo = {};
        }
      } catch (err) {
        console.error("验证 token 失败:", err);
      }
    },
    // 跳转到登录页面
    goToLogin() {
      common_vendor.index.navigateTo({
        url: "/pages/my/login"
      });
    },
    // 编辑个人资料
    editProfile() {
      common_vendor.index.navigateTo({
        url: "/pages/my/profile"
      });
    },
    // 功能导航
    navigateToFunction(item) {
      if (item.requireLogin && !this.isLoggedIn) {
        common_vendor.index.showToast({
          title: "请先登录",
          icon: "none"
        });
        setTimeout(() => {
          this.goToLogin();
        }, 1500);
        return;
      }
      common_vendor.index.navigateTo({
        url: item.url
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isLoggedIn
  }, $data.isLoggedIn ? {
    b: $data.userInfo.avatar || "/static/images/default-avatar.png",
    c: common_vendor.t($data.userInfo.nickname || "未设置昵称"),
    d: common_vendor.t($data.userInfo._id || "未获取"),
    e: common_vendor.o((...args) => $options.editProfile && $options.editProfile(...args))
  } : {
    f: common_assets._imports_0$1,
    g: common_vendor.o((...args) => $options.goToLogin && $options.goToLogin(...args))
  }, {
    h: common_vendor.f($data.functionList, (item, index, i0) => {
      return {
        a: item.icon,
        b: common_vendor.n(item.bgColor),
        c: common_vendor.t(item.name),
        d: item.id,
        e: common_vendor.o(($event) => $options.navigateToFunction(item), item.id)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
