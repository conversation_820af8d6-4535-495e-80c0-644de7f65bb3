// pages/my/my.js
Page({
  data: {
    userInfo: {
      avatar: '/static/icons/user-avatar.png',
      nickname: '用户昵称',
      isLoggedIn: false
    },
    menuItems: [
      { id: 'posts', name: '我的发布', icon: '/static/icons/发布.png' },
      { id: 'favorites', name: '我的收藏', icon: '/static/icons/我的收藏.png' },
      { id: 'orders', name: '我的订单', icon: '/static/icons/购物车.png' },
      { id: 'wallet', name: '我的钱包', icon: '/static/icons/我的钱包.png' },
      { id: 'settings', name: '设置', icon: '/static/icons/设置.png' }
    ]
  },

  onLoad: function(options) {
    this.checkLoginStatus();
  },

  onShow: function() {
    this.checkLoginStatus();
  },

  checkLoginStatus: function() {
    var token = wx.getStorageSync('token');
    var userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.setData({
        'userInfo.isLoggedIn': true,
        'userInfo.nickname': userInfo.nickname || '用户昵称',
        'userInfo.avatar': userInfo.avatar || '/static/icons/user-avatar.png'
      });
    } else {
      this.setData({
        'userInfo.isLoggedIn': false,
        'userInfo.nickname': '点击登录',
        'userInfo.avatar': '/static/icons/user-avatar.png'
      });
    }
  },

  handleLogin: function() {
    if (!this.data.userInfo.isLoggedIn) {
      wx.navigateTo({
        url: '/pages/my/login'
      });
    }
  },

  navigateToPage: function(e) {
    var pageId = e.currentTarget.dataset.id;
    
    // 检查是否需要登录
    if (!this.data.userInfo.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/my/' + pageId
    });
  }
})
