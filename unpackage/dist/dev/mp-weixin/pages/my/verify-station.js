"use strict";
const common_vendor = require("../../common/vendor.js");
const api_station = require("../../api/station.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      // 认证状态：null(未申请), pending(审核中), approved(已通过), rejected(已拒绝)
      verificationStatus: null,
      formData: {
        stationName: "",
        stationCode: "",
        stationType: "",
        brand: "",
        address: "",
        longitude: null,
        latitude: null,
        contactName: "",
        contactPhone: "",
        businessLicense: "",
        authorization: "",
        storefront: "",
        interior: "",
        agreement: false
      },
      stationTypes: ["社区驿站", "快递驿站", "校园驿站", "写字楼驿站", "商业驿站"],
      brands: ["顺丰", "京东", "菜鸟", "中通", "圆通", "申通", "韵达", "百世", "其他"],
      statusIcon: {
        pending: "/static/icons/processing.png",
        approved: "/static/icons/success.png",
        rejected: "/static/icons/error.png"
      },
      statusText: {
        pending: "审核中",
        approved: "认证成功",
        rejected: "认证失败"
      },
      statusDesc: {
        pending: "您的驿站认证申请已提交，正在审核中，请耐心等待...",
        approved: "恭喜您，驿站认证已通过！您现在可以使用驿站功能。",
        rejected: "很抱歉，您的驿站认证未通过审核，请检查认证信息或联系客服。"
      }
    };
  },
  computed: {
    isFormValid() {
      const {
        stationName,
        stationCode,
        stationType,
        brand,
        address,
        contactName,
        contactPhone,
        businessLicense,
        storefront,
        interior,
        agreement
      } = this.formData;
      return stationName && stationCode && stationType && brand && address && contactName && contactPhone && businessLicense && storefront && interior && agreement;
    },
    isDisabled() {
      return this.verificationStatus === "pending" || this.verificationStatus === "approved";
    }
  },
  onLoad() {
    this.checkVerificationStatus();
  },
  methods: {
    // 检查认证状态
    async checkVerificationStatus() {
      try {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        const result = await api_station.getStationVerificationStatus();
        if (result.code === 0) {
          const { status, verification, remark } = result.data;
          this.verificationStatus = status;
          if (status === "rejected" && verification) {
            this.formData = {
              ...this.formData,
              stationName: verification.stationName,
              stationCode: verification.stationCode,
              stationType: this.mapStationType(verification.stationType),
              brand: verification.brand,
              address: verification.address,
              longitude: verification.longitude,
              latitude: verification.latitude,
              contactName: verification.contactName,
              contactPhone: verification.contactPhone,
              businessLicense: verification.businessLicense,
              authorization: verification.authorization,
              storefront: verification.storefront,
              interior: verification.interior,
              agreement: true
            };
            if (remark) {
              setTimeout(() => {
                common_vendor.index.showModal({
                  title: "认证被拒绝",
                  content: `拒绝原因: ${remark}`,
                  showCancel: false
                });
              }, 500);
            }
          }
        }
      } catch (error) {
        console.error("获取认证状态失败:", error);
        common_vendor.index.showToast({
          title: "获取认证状态失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 类型映射
    mapStationType(type) {
      const typeMap = {
        "community": "社区驿站",
        "express": "快递驿站",
        "campus": "校园驿站",
        "office": "写字楼驿站",
        "commercial": "商业驿站"
      };
      return typeMap[type] || type;
    },
    // 反向类型映射
    reverseMapStationType(displayType) {
      const reverseTypeMap = {
        "社区驿站": "community",
        "快递驿站": "express",
        "校园驿站": "campus",
        "写字楼驿站": "office",
        "商业驿站": "commercial"
      };
      return reverseTypeMap[displayType] || displayType;
    },
    // 选择驿站类型
    onStationTypeChange(e) {
      const index = e.detail.value;
      this.formData.stationType = this.stationTypes[index];
    },
    // 选择品牌
    onBrandChange(e) {
      const index = e.detail.value;
      this.formData.brand = this.brands[index];
    },
    // 使用当前位置
    useCurrentLocation() {
      common_vendor.index.getLocation({
        type: "gcj02",
        success: (res) => {
          this.formData.longitude = res.longitude;
          this.formData.latitude = res.latitude;
          common_vendor.index.chooseLocation({
            latitude: res.latitude,
            longitude: res.longitude,
            success: (location) => {
              this.formData.address = location.address + location.name;
            }
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "获取位置失败，请手动输入地址",
            icon: "none"
          });
        }
      });
    },
    // 上传图片
    async uploadImage(type) {
      if (this.isDisabled)
        return;
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: async (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.showLoading({
            title: "上传中..."
          });
          try {
            const result = await api_station.uploadVerificationImage(tempFilePath, type);
            if (result.code === 0) {
              this.formData[type] = result.data.url;
              common_vendor.index.showToast({
                title: "上传成功",
                icon: "success"
              });
            } else {
              common_vendor.index.showToast({
                title: result.message || "上传失败",
                icon: "none"
              });
            }
          } catch (error) {
            console.error("上传图片失败:", error);
            common_vendor.index.showToast({
              title: "上传失败，请重试",
              icon: "none"
            });
          } finally {
            common_vendor.index.hideLoading();
          }
        }
      });
    },
    // 删除图片
    deleteImage(type) {
      if (this.isDisabled)
        return;
      common_vendor.index.showModal({
        title: "删除图片",
        content: "确定要删除该图片吗？",
        success: (res) => {
          if (res.confirm) {
            this.formData[type] = "";
          }
        }
      });
    },
    // 切换协议同意状态
    toggleAgreement() {
      if (this.isDisabled)
        return;
      this.formData.agreement = !this.formData.agreement;
    },
    // 查看服务条款
    viewServiceTerms() {
      common_vendor.index.navigateTo({
        url: "/pages/common/agreement?type=station"
      });
    },
    // 提交认证
    async submitVerification() {
      if (!this.isFormValid || this.isDisabled)
        return;
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      try {
        const submitData = {
          ...this.formData,
          stationType: this.reverseMapStationType(this.formData.stationType)
        };
        let result;
        if (this.verificationStatus === "rejected") {
          result = await api_station.resubmitStationVerification(submitData);
        } else {
          result = await api_station.submitStationVerification(submitData);
        }
        if (result.code === 0) {
          common_vendor.index.showToast({
            title: "提交成功",
            icon: "success"
          });
          this.verificationStatus = "pending";
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: result.message || "提交失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("提交认证申请失败:", error);
        common_vendor.index.showToast({
          title: "提交失败，请重试",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 显示帮助信息
    showHelp() {
      common_vendor.index.showModal({
        title: "驿站认证帮助",
        content: "驿站认证是为了确认您的驿站身份，通过认证后可以使用平台提供的驿站功能。如有疑问，请联系客服。",
        showCancel: false
      });
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_assets._imports_1$5,
    d: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    e: $data.verificationStatus
  }, $data.verificationStatus ? {
    f: $data.statusIcon[$data.verificationStatus],
    g: common_vendor.t($data.statusText[$data.verificationStatus]),
    h: common_vendor.t($data.statusDesc[$data.verificationStatus])
  } : {}, {
    i: $data.verificationStatus !== "approved" && $data.verificationStatus !== "pending"
  }, $data.verificationStatus !== "approved" && $data.verificationStatus !== "pending" ? common_vendor.e({
    j: $options.isDisabled,
    k: $data.formData.stationName,
    l: common_vendor.o(($event) => $data.formData.stationName = $event.detail.value),
    m: $options.isDisabled,
    n: $data.formData.stationCode,
    o: common_vendor.o(($event) => $data.formData.stationCode = $event.detail.value),
    p: common_vendor.t($data.formData.stationType || "请选择驿站类型"),
    q: common_assets._imports_2$7,
    r: $data.stationTypes,
    s: common_vendor.o((...args) => $options.onStationTypeChange && $options.onStationTypeChange(...args)),
    t: $options.isDisabled,
    v: common_vendor.t($data.formData.brand || "请选择所属品牌"),
    w: common_assets._imports_2$7,
    x: $data.brands,
    y: common_vendor.o((...args) => $options.onBrandChange && $options.onBrandChange(...args)),
    z: $options.isDisabled,
    A: $options.isDisabled,
    B: $data.formData.address,
    C: common_vendor.o(($event) => $data.formData.address = $event.detail.value),
    D: !$options.isDisabled
  }, !$options.isDisabled ? {
    E: common_vendor.o((...args) => $options.useCurrentLocation && $options.useCurrentLocation(...args))
  } : {}, {
    F: $options.isDisabled,
    G: $data.formData.contactName,
    H: common_vendor.o(($event) => $data.formData.contactName = $event.detail.value),
    I: $options.isDisabled,
    J: $data.formData.contactPhone,
    K: common_vendor.o(($event) => $data.formData.contactPhone = $event.detail.value),
    L: !$data.formData.businessLicense || $options.isDisabled
  }, !$data.formData.businessLicense || $options.isDisabled ? common_vendor.e({
    M: $data.formData.businessLicense
  }, $data.formData.businessLicense ? {
    N: $data.formData.businessLicense
  } : {
    O: common_assets._imports_3$7
  }, {
    P: common_vendor.o(($event) => $options.uploadImage("businessLicense"))
  }) : {
    Q: $data.formData.businessLicense,
    R: common_assets._imports_2$1,
    S: common_vendor.o(($event) => $options.deleteImage("businessLicense"))
  }, {
    T: !$data.formData.authorization || $options.isDisabled
  }, !$data.formData.authorization || $options.isDisabled ? common_vendor.e({
    U: $data.formData.authorization
  }, $data.formData.authorization ? {
    V: $data.formData.authorization
  } : {
    W: common_assets._imports_3$7
  }, {
    X: common_vendor.o(($event) => $options.uploadImage("authorization"))
  }) : {
    Y: $data.formData.authorization,
    Z: common_assets._imports_2$1,
    aa: common_vendor.o(($event) => $options.deleteImage("authorization"))
  }, {
    ab: !$data.formData.storefront || $options.isDisabled
  }, !$data.formData.storefront || $options.isDisabled ? common_vendor.e({
    ac: $data.formData.storefront
  }, $data.formData.storefront ? {
    ad: $data.formData.storefront
  } : {
    ae: common_assets._imports_3$7
  }, {
    af: common_vendor.o(($event) => $options.uploadImage("storefront"))
  }) : {
    ag: $data.formData.storefront,
    ah: common_assets._imports_2$1,
    ai: common_vendor.o(($event) => $options.deleteImage("storefront"))
  }, {
    aj: !$data.formData.interior || $options.isDisabled
  }, !$data.formData.interior || $options.isDisabled ? common_vendor.e({
    ak: $data.formData.interior
  }, $data.formData.interior ? {
    al: $data.formData.interior
  } : {
    am: common_assets._imports_3$7
  }, {
    an: common_vendor.o(($event) => $options.uploadImage("interior"))
  }) : {
    ao: $data.formData.interior,
    ap: common_assets._imports_2$1,
    aq: common_vendor.o(($event) => $options.deleteImage("interior"))
  }, {
    ar: $data.formData.agreement
  }, $data.formData.agreement ? {} : {}, {
    as: common_vendor.o((...args) => $options.toggleAgreement && $options.toggleAgreement(...args)),
    at: common_vendor.o((...args) => $options.viewServiceTerms && $options.viewServiceTerms(...args)),
    av: common_vendor.t($data.verificationStatus === "rejected" ? "重新提交" : "提交认证"),
    aw: !$options.isFormValid || $options.isDisabled ? 1 : "",
    ax: common_vendor.o((...args) => $options.submitVerification && $options.submitVerification(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
