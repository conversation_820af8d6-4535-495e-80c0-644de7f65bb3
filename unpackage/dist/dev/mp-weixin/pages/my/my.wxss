/* pages/my/my.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.user-section {
  background-color: #fff;
  padding: 40rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-nickname {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.user-status {
  font-size: 24rpx;
  color: #999;
}

.arrow-icon {
  width: 30rpx;
  height: 30rpx;
}

.menu-section {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 0 20rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 30rpx;
}

.menu-name {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}
