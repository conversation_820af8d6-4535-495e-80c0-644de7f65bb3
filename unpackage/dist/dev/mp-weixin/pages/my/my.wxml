<!--pages/my/my.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section" bindtap="handleLogin">
    <image src="{{userInfo.avatar}}" mode="aspectFill" class="user-avatar"></image>
    <view class="user-info">
      <text class="user-nickname">{{userInfo.nickname}}</text>
      <text class="user-status" wx:if="{{!userInfo.isLoggedIn}}">点击登录</text>
      <text class="user-status" wx:else>已登录</text>
    </view>
    <image src="/static/icons/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
  </view>
  
  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" wx:for="{{menuItems}}" wx:key="id" 
          data-id="{{item.id}}" bindtap="navigateToPage">
      <image src="{{item.icon}}" mode="aspectFit" class="menu-icon"></image>
      <text class="menu-name">{{item.name}}</text>
      <image src="/static/icons/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
    </view>
  </view>
</view>
