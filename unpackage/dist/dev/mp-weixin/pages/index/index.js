// pages/index/index.js
Page({
  data: {
    banners: [
      { image: '/static/images/banner1.jpg' },
      { image: '/static/images/banner2.jpg' }
    ],
    stationList: [
      {
        id: 1,
        title: '城北社区驿站转让',
        address: '海淀区北太平庄',
        price: '45000',
        priceUnit: '元',
        image: '/static/images/station1.jpg'
      },
      {
        id: 2,
        title: '校园驿站急转',
        address: '朝阳区北苑路',
        price: '3800',
        priceUnit: '元/月',
        image: '/static/images/station2.jpg'
      }
    ],
    jobList: [
      {
        id: 1,
        title: '驿站分拣员',
        salary: '4500-5500元/月',
        company: '城北速递驿站',
        tags: ['五险', '餐补', '全职']
      },
      {
        id: 2,
        title: '快递配送员',
        salary: '8000-10000元/月',
        company: '优速快递',
        tags: ['无需经验', '兼职可选', '多劳多得']
      }
    ],
    serviceList: [
      {
        id: 1,
        title: '社区驿站收发员顶班',
        time: '09:00-18:00',
        address: '海淀区中关村南大街',
        price: '200元/天'
      },
      {
        id: 2,
        title: '快递分拣打包顶班',
        time: '18:00-22:00',
        address: '朝阳区望京西园',
        price: '35元/小时'
      }
    ],
    // 搜索相关数据
    searchKeyword: '',
    showSearchPanel: false,
    searchHistory: [],
    searchResults: [],
    isSearching: false,
    hotSearches: [
      '驿站转让',
      '电子秤',
      '驿站工作',
      '顶班服务',
      '快递柜',
      '打印机',
      '收银系统',
      '货架'
    ]
  },

  onLoad: function() {
    // 页面加载时从本地存储加载搜索历史
    this.loadSearchHistory();
  },

  navigateTo: function(e) {
    var url = e.currentTarget.dataset.url;
    // 检查是否为 tabBar 页面
    if (url === '/pages/station/station') {
      wx.switchTab({
        url: url
      });
    } else {
      wx.navigateTo({
        url: url
      });
    }
  },

  viewDetail: function(e) {
    var item = e.currentTarget.dataset.item;
    wx.navigateTo({
      url: '/pages/detail/detail?id=' + item.id + '&type=' + this.getTypeFromItem(item)
    });
  },

  getTypeFromItem: function(item) {
    if(item.priceUnit) return 'station';
    if(item.salary) return 'job';
    if(item.time) return 'service';
    return 'other';
  },

  // 打开搜索面板
  showSearch: function() {
    this.setData({
      showSearchPanel: true
    });
  },

  // 关闭搜索面板
  closeSearchPanel: function() {
    this.setData({
      showSearchPanel: false,
      searchKeyword: '',
      searchResults: [],
      isSearching: false
    });
  },

  // 清除搜索关键词
  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      searchResults: [],
      isSearching: false
    });
  },

  // 使用历史关键词
  useHistoryKeyword: function(e) {
    var keyword = e.currentTarget.dataset.keyword;
    this.setData({
      searchKeyword: keyword
    });
    this.performLiveSearch();
  },

  // 加载搜索历史
  loadSearchHistory: function() {
    var history = wx.getStorageSync('searchHistory');
    if (history) {
      this.setData({
        searchHistory: JSON.parse(history)
      });
    }
  },

  // 保存搜索历史
  saveSearchHistory: function(keyword) {
    // 如果关键词为空，不保存
    if (!keyword.trim()) return;

    // 移除旧的相同关键词
    var history = this.data.searchHistory.filter(function(item) {
      return item !== keyword;
    });

    // 添加到历史开头
    history.unshift(keyword);

    // 只保留最近10条
    if (history.length > 10) {
      history = history.slice(0, 10);
    }

    // 更新本地和数据
    this.setData({
      searchHistory: history
    });
    wx.setStorageSync('searchHistory', JSON.stringify(history));
  },

  // 清空搜索历史
  clearHistory: function() {
    var that = this;
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索历史吗？',
      success: function(res) {
        if (res.confirm) {
          that.setData({
            searchHistory: []
          });
          wx.removeStorageSync('searchHistory');
        }
      }
    });
  },

  // 搜索输入变化
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    this.performLiveSearch();
  },

  // 执行实时搜索
  performLiveSearch: function() {
    // 如果关键词为空，清空结果
    if (!this.data.searchKeyword.trim()) {
      this.setData({
        searchResults: [],
        isSearching: false
      });
      return;
    }

    this.setData({
      isSearching: true
    });

    // 搜索所有列表数据
    var keyword = this.data.searchKeyword.toLowerCase();
    var results = [];

    // 搜索驿站转让
    this.data.stationList.forEach(function(item) {
      if (item.title.toLowerCase().indexOf(keyword) !== -1 ||
          item.address.toLowerCase().indexOf(keyword) !== -1) {
        results.push(Object.assign({}, item, {type: 'station'}));
      }
    });

    // 搜索招聘求职
    this.data.jobList.forEach(function(item) {
      if (item.title.toLowerCase().indexOf(keyword) !== -1 ||
          item.company.toLowerCase().indexOf(keyword) !== -1) {
        results.push(Object.assign({}, item, {type: 'job'}));
      }
    });

    // 搜索顶班服务
    this.data.serviceList.forEach(function(item) {
      if (item.title.toLowerCase().indexOf(keyword) !== -1 ||
          item.address.toLowerCase().indexOf(keyword) !== -1) {
        results.push(Object.assign({}, item, {type: 'service'}));
      }
    });

    this.setData({
      searchResults: results,
      isSearching: false
    });
  },

  // 处理搜索提交
  handleSearch: function() {
    // 保存到搜索历史
    this.saveSearchHistory(this.data.searchKeyword);

    // 根据关键词判断跳转目标页面
    var targetPage = '';
    var keyword = this.data.searchKeyword.toLowerCase();

    // 根据关键词智能匹配目标页面
    if (keyword.indexOf('电子秤') !== -1 || keyword.indexOf('打印机') !== -1 ||
        keyword.indexOf('货架') !== -1 || keyword.indexOf('设备') !== -1) {
      targetPage = '/pages/device/device';
    } else if (keyword.indexOf('驿站') !== -1 || keyword.indexOf('转让') !== -1) {
      targetPage = '/pages/transfer/transfer';
    } else if (keyword.indexOf('招聘') !== -1 || keyword.indexOf('求职') !== -1 ||
               keyword.indexOf('工作') !== -1) {
      targetPage = '/pages/job/job';
    } else if (keyword.indexOf('顶班') !== -1 || keyword.indexOf('服务') !== -1) {
      targetPage = '/pages/service/service';
    } else {
      // 默认跳转到设备页面
      targetPage = '/pages/device/device';
    }

    // 关闭搜索面板
    this.closeSearchPanel();

    // 跳转到对应页面并携带搜索关键词
    wx.navigateTo({
      url: targetPage + '?keyword=' + encodeURIComponent(this.data.searchKeyword)
    });
  }
})