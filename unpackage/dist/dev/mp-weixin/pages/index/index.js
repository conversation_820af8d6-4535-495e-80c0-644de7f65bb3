// pages/index/index.js
Page({
  data: {
    banners: [
      { image: '/static/images/banner1.jpg' },
      { image: '/static/images/banner2.jpg' }
    ],
    stationList: [
      {
        id: 1,
        title: '城北社区驿站转让',
        address: '海淀区北太平庄',
        price: '45000',
        priceUnit: '元',
        image: '/static/images/station1.jpg'
      },
      {
        id: 2,
        title: '校园驿站急转',
        address: '朝阳区北苑路',
        price: '3800',
        priceUnit: '元/月',
        image: '/static/images/station2.jpg'
      }
    ],
    jobList: [
      {
        id: 1,
        title: '驿站分拣员',
        salary: '4500-5500元/月',
        company: '城北速递驿站',
        tags: ['五险', '餐补', '全职']
      },
      {
        id: 2,
        title: '快递配送员',
        salary: '8000-10000元/月',
        company: '优速快递',
        tags: ['无需经验', '兼职可选', '多劳多得']
      }
    ],
    serviceList: [
      {
        id: 1,
        title: '社区驿站收发员顶班',
        time: '09:00-18:00',
        address: '海淀区中关村南大街',
        price: '200元/天'
      },
      {
        id: 2,
        title: '快递分拣打包顶班',
        time: '18:00-22:00',
        address: '朝阳区望京西园',
        price: '35元/小时'
      }
    ]
  },

  onLoad: function() {
    console.log('Index page loaded');
  },

  navigateTo: function(e) {
    var url = e.currentTarget.dataset.url;
    // 检查是否为 tabBar 页面
    if (url === '/pages/station/station') {
      wx.switchTab({
        url: url
      });
    } else {
      wx.navigateTo({
        url: url
      });
    }
  },

  viewDetail: function(e) {
    var item = e.currentTarget.dataset.item;
    console.log('View detail:', item);
    wx.showToast({
      title: '查看详情',
      icon: 'none'
    });
  }
})