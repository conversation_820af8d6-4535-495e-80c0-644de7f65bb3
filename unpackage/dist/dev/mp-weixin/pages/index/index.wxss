/* pages/index/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.city-selector {
  font-size: 32rpx;
  font-weight: bold;
  padding-right: 20rpx;
}

.search-bar {
  flex: 1;
  background-color: #eee;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  height: 60rpx;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 轮播图样式 */
.banner {
  height: 300rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.banner image {
  width: 100%;
  height: 100%;
}

/* 功能导航样式 */
.nav-section {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-item image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.nav-item text {
  font-size: 24rpx;
  color: #333;
}

/* 内容区域样式 */
.section {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
}

.view-more {
  color: #999;
  font-size: 24rpx;
}

/* 驿站转让列表样式 */
.station-list {
  display: flex;
  flex-direction: column;
}

.station-item {
  display: flex;
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.station-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.station-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.station-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.station-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.station-address {
  color: #999;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}

.station-price {
  color: #ff5a5f;
  font-size: 32rpx;
  font-weight: bold;
}

.price-unit {
  font-size: 24rpx;
  font-weight: normal;
}

/* 招聘求职列表样式 */
.job-list {
  display: flex;
  flex-direction: column;
}

.job-item {
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.job-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.job-title {
  font-size: 32rpx;
  font-weight: bold;
}

.job-salary {
  color: #ff5a5f;
  font-size: 28rpx;
  font-weight: bold;
}

.job-company {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.job-tags {
  display: flex;
  flex-wrap: wrap;
}

.job-tag {
  background-color: #f0f0f0;
  color: #666;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

/* 顶班服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.service-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.service-info {
  flex: 1;
}

.service-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.service-time {
  color: #666;
  font-size: 24rpx;
  margin-bottom: 5rpx;
}

.service-address {
  color: #999;
  font-size: 24rpx;
}

.service-price {
  color: #ff5a5f;
  font-size: 28rpx;
  font-weight: bold;
}
