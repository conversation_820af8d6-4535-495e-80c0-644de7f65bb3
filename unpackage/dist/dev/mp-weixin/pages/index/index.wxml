<!--pages/index/index.wxml-->
<view class="container">
  <!-- 顶部城市选择和搜索框 -->
  <view class="header">
    <view class="city-selector">北京市</view>
    <view class="search-bar">
      <image src="/static/icons/search.png" mode="aspectFit" class="search-icon"></image>
      <text class="search-placeholder">搜索驿站/设备/职位</text>
    </view>
  </view>

  <!-- 轮播图区域 -->
  <swiper class="banner" indicator-dots autoplay circular>
    <swiper-item wx:for="{{banners}}" wx:key="index">
      <image src="{{item.image}}" mode="aspectFill"></image>
    </swiper-item>
  </swiper>

  <!-- 功能导航区域 -->
  <view class="nav-section">
    <view class="nav-item" bindtap="navigateTo" data-url="/pages/transfer/transfer">
      <image src="/static/icons/station.png" mode="aspectFit"></image>
      <text>驿站转让</text>
    </view>
    <view class="nav-item" bindtap="navigateTo" data-url="/pages/device/device">
      <image src="/static/icons/device.png" mode="aspectFit"></image>
      <text>设备交易</text>
    </view>
    <view class="nav-item" bindtap="navigateTo" data-url="/pages/job/job">
      <image src="/static/icons/job.png" mode="aspectFit"></image>
      <text>招聘求职</text>
    </view>
    <view class="nav-item" bindtap="navigateTo" data-url="/pages/service/service">
      <image src="/static/icons/service.png" mode="aspectFit"></image>
      <text>顶班服务</text>
    </view>
  </view>

  <!-- 驿站转让区域 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">驿站转让</text>
      <view class="view-more" bindtap="navigateTo" data-url="/pages/transfer/transfer">查看更多 ></view>
    </view>
    <view class="station-list">
      <view class="station-item" wx:for="{{stationList}}" wx:key="id" data-item="{{item}}" bindtap="viewDetail">
        <image src="{{item.image}}" mode="aspectFill" class="station-image"></image>
        <view class="station-info">
          <view class="station-title">{{item.title}}</view>
          <view class="station-address">{{item.address}}</view>
          <view class="station-price">¥{{item.price}}<text class="price-unit">{{item.priceUnit}}</text></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 招聘求职区域 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">招聘求职</text>
      <view class="view-more" bindtap="navigateTo" data-url="/pages/job/job">查看更多 ></view>
    </view>
    <view class="job-list">
      <view class="job-item" wx:for="{{jobList}}" wx:key="id" data-item="{{item}}" bindtap="viewDetail">
        <view class="job-header">
          <view class="job-title">{{item.title}}</view>
          <view class="job-salary">{{item.salary}}</view>
        </view>
        <view class="job-company">{{item.company}}</view>
        <view class="job-tags">
          <text class="job-tag" wx:for="{{item.tags}}" wx:for-item="tag" wx:key="*this">{{tag}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 顶班服务区域 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">顶班服务</text>
      <view class="view-more" bindtap="navigateTo" data-url="/pages/service/service">查看更多 ></view>
    </view>
    <view class="service-list">
      <view class="service-item" wx:for="{{serviceList}}" wx:key="id" data-item="{{item}}" bindtap="viewDetail">
        <view class="service-info">
          <view class="service-title">{{item.title}}</view>
          <view class="service-time">{{item.time}}</view>
          <view class="service-address">{{item.address}}</view>
        </view>
        <view class="service-price">{{item.price}}</view>
      </view>
    </view>
  </view>
</view>