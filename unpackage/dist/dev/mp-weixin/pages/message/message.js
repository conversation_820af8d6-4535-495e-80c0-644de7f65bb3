// pages/message/message.js
Page({
  data: {
    messages: [
      {
        id: 1,
        type: 'system',
        title: '系统通知',
        content: '欢迎使用驿站帮Pro！',
        time: '2024-01-01 10:00',
        unread: true
      },
      {
        id: 2,
        type: 'order',
        title: '订单消息',
        content: '您的订单已确认',
        time: '2024-01-01 09:30',
        unread: false
      }
    ]
  },

  onLoad: function(options) {
    console.log('Message page loaded');
  },

  viewMessage: function(e) {
    var messageId = e.currentTarget.dataset.id;
    console.log('View message:', messageId);
    // 标记为已读
    var messages = this.data.messages.map(function(msg) {
      if (msg.id === messageId) {
        msg.unread = false;
      }
      return msg;
    });
    this.setData({
      messages: messages
    });
  }
})
