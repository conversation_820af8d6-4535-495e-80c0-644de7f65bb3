import request from './request';

/**
 * 提交驿站认证申请
 * @param {Object} data 驿站认证信息
 * @returns {Promise}
 */
export function submitStationVerification(data) {
  return request.post('/api/station/verify', data);
}

/**
 * 获取驿站认证状态
 * @returns {Promise}
 */
export function getStationVerificationStatus() {
  return request.get('/api/station/verify/status');
}

/**
 * 上传认证图片
 * @param {String} filePath 文件路径
 * @param {String} type 图片类型 (license/authorization/storefront/interior)
 * @returns {Promise}
 */
export function uploadVerificationImage(filePath, type) {
  return request.upload('/api/upload/verification', filePath, 'file', { type });
}

/**
 * 重新提交驿站认证申请
 * @param {Object} data 驿站认证信息
 * @returns {Promise}
 */
export function resubmitStationVerification(data) {
  return request.put('/api/station/verify', data);
}

export default {
  submitStationVerification,
  getStationVerificationStatus,
  uploadVerificationImage,
  resubmitStationVerification
}; 