/**
 * 认证相关API
 */
import request from '../request';

/**
 * 微信登录
 * 通过wx.login获取code，然后发送到服务器换取登录凭证
 */
export function wxLogin() {
  return new Promise((resolve, reject) => {
    console.log('准备调用微信登录接口...');
    
    // 调用微信登录接口获取code
    wx.login({
      timeout: 10000, // 设置超时时间为10秒
      success: (res) => {
        console.log('微信登录接口返回:', res);
        
        if (res.code) {
          // 获取code成功，发送到服务器换取openid和token
          console.log('获取code成功，开始请求后端登录API...');
          
          // 获取用户信息（可选）
          let userInfo = wx.getStorageSync('wxUserInfo') || null;
          
          // 构建请求数据
          const requestData = {
            code: res.code,
            userInfo: userInfo
          };
          
          console.log('发送登录请求数据:', requestData);
          
          request.post('/api/auth/wx-login', requestData)
            .then(response => {
              console.log('登录成功，服务器返回:', response);
              
              // 保存token到本地存储
              if (response.data && response.data.token) {
                wx.setStorageSync('token', response.data.token);
                wx.setStorageSync('userInfo', JSON.stringify(response.data.user || {}));
                
                // 检查是否是新用户
                if (response.data.isNewUser) {
                  console.log('新用户登录，可能需要引导完善资料');
                }
              }
              resolve(response);
            })
            .catch(error => {
              console.error('登录请求失败:', error);
              
              // 错误提示
              wx.showToast({
                title: error.message || '登录失败，请稍后再试',
                icon: 'none',
                duration: 2000
              });
              
              reject(error);
            });
        } else {
          console.error('获取code失败:', res.errMsg);
          
          wx.showToast({
            title: '微信登录失败: ' + (res.errMsg || '未知错误'),
            icon: 'none',
            duration: 2000
          });
          
          reject(new Error('登录失败: ' + (res.errMsg || '未知错误')));
        }
      },
      fail: (err) => {
        console.error('微信登录接口调用失败:', err);
        
        wx.showToast({
          title: '微信登录接口调用失败',
          icon: 'none',
          duration: 2000
        });
        
        reject(new Error('微信登录接口调用失败: ' + (err.errMsg || '未知错误')));
      }
    });
  });
}

/**
 * 检查登录状态
 * @returns {boolean} 是否已登录
 */
export function checkLogin() {
  const token = wx.getStorageSync('token');
  return !!token;
}

/**
 * 获取本地存储的用户信息
 * @returns {object} 用户信息
 */
export function getUserInfo() {
  return wx.getStorageSync('userInfo') || {};
}

/**
 * 退出登录
 */
export function logout() {
  wx.removeStorageSync('token');
  wx.removeStorageSync('userInfo');
  return Promise.resolve();
}

/**
 * 使用手机号和密码登录
 * @param {string} phone 手机号
 * @param {string} password 密码
 */
export function loginByPhone(phone, password) {
  return request.post('/api/auth/login', {
    phone,
    password
  }).then(response => {
    // 保存token到本地存储
    if (response.data && response.data.token) {
      wx.setStorageSync('token', response.data.token);
      wx.setStorageSync('userInfo', JSON.stringify(response.data.userInfo || {}));
    }
    return response;
  });
}

/**
 * 使用手机号和验证码登录
 * @param {string} phone 手机号
 * @param {string} code 验证码
 */
export function loginByPhoneCode(phone, code) {
  return request.post('/api/auth/login-by-code', {
    phone,
    code
  }).then(response => {
    // 保存token到本地存储
    if (response.data && response.data.token) {
      wx.setStorageSync('token', response.data.token);
      wx.setStorageSync('userInfo', JSON.stringify(response.data.userInfo || {}));
    }
    return response;
  });
}

/**
 * 发送手机验证码
 * @param {string} phone 手机号
 * @param {string} type 验证码类型: login/register/reset
 */
export function sendSmsCode(phone, type = 'login') {
  return request.post('/api/auth/send-code', {
    phone,
    type
  });
}

/**
 * 注册账号
 * @param {object} data 注册信息
 */
export function register(data) {
  return request.post('/api/auth/register', data);
}

export default {
  wxLogin,
  checkLogin,
  getUserInfo,
  logout,
  loginByPhone,
  loginByPhoneCode,
  sendSmsCode,
  register
}; 