import request from './request';

/**
 * 认证相关API
 */

/**
 * 用户登录
 * @param {Object} data - 登录信息
 * @param {String} data.phone - 手机号
 * @param {String} data.password - 密码
 * @returns {Promise} - 请求结果
 */
export function login(data) {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  });
}

/**
 * 用户注册
 * @param {Object} data - 注册信息
 * @param {String} data.phone - 手机号
 * @param {String} data.password - 密码
 * @param {String} data.nickname - 昵称
 * @param {String} data.avatar - 头像
 * @returns {Promise} - 请求结果
 */
export function register(data) {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  });
}

/**
 * 检查手机号是否已注册
 * @param {String} phone - 手机号
 * @returns {Promise} - 请求结果
 */
export function checkPhone(phone) {
  return request({
    url: '/auth/check-phone',
    method: 'get',
    params: { phone }
  });
}

/**
 * 重置密码
 * @param {Object} data - 重置密码信息
 * @param {String} data.phone - 手机号
 * @param {String} data.newPassword - 新密码
 * @returns {Promise} - 请求结果
 */
export function resetPassword(data) {
  return request({
    url: '/auth/reset-password',
    method: 'post',
    data
  });
}

/**
 * 获取登录状态
 * @returns {Promise} - 请求结果
 */
export function getLoginStatus() {
  return request({
    url: '/auth/status',
    method: 'get'
  });
}

/**
 * 更新用户信息
 * @param {Object} data - 用户信息
 * @param {String} data.nickname - 昵称
 * @param {String} data.avatar - 头像
 * @returns {Promise} - 请求结果
 */
export function updateProfile(data) {
  return request({
    url: '/auth/profile',
    method: 'put',
    data
  });
}

/**
 * 发送短信验证码
 * @param {string} phone 手机号
 * @param {string} type 验证码类型 (login/register/resetPassword)
 */
export function sendSmsCode(phone, type = 'login') {
  return request.post('/api/sms/send', { phone, type });
}

/**
 * 使用手机号和验证码登录
 * @param {string} phone 手机号
 * @param {string} code 验证码
 */
export function loginByPhone(phone, code) {
  return request.post('/api/auth/phone-login', { phone, code });
}

/**
 * 使用手机号和密码登录
 * @param {string} phone 手机号
 * @param {string} password 密码
 */
export function loginByPassword(phone, password) {
  return request.post('/api/auth/password-login', { phone, password });
}

/**
 * 微信登录
 * @param {Object} data 登录参数
 * @param {string} data.code 微信登录返回的code
 * @param {Object} data.userInfo 微信用户信息
 */
export function wxLogin(data) {
  return request.post('/api/auth/wx-login', data);
}

/**
 * 退出登录
 */
export function logout() {
  return request.post('/api/auth/logout');
}

/**
 * 刷新令牌
 */
export function refreshToken() {
  return request.post('/api/auth/refresh-token');
} 