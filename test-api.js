// API测试脚本
const axios = require('axios');

async function testCheckPhone() {
  try {
    console.log('测试 /api/auth/check-phone 接口...');
    console.log('请求参数: phone=17697657892');
    const response = await axios.get('http://localhost:3000/api/auth/check-phone?phone=17697657892');
    console.log('API 响应:', response.data);
    return response.data;
  } catch (error) {
    console.error('API 错误:', error.message);
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误详情:', error.response.data);
    } else if (error.request) {
      console.error('没有收到响应');
    } else {
      console.error('请求设置错误:', error.message);
    }
    console.error('错误配置:', error.config);
  }
}

async function testSendSms() {
  try {
    console.log('测试 /api/sms/send 接口...');
    console.log('请求参数:', { phone: '17697657892', type: 'login' });
    const response = await axios.post('http://localhost:3000/api/sms/send', {
      phone: '17697657892',
      type: 'login'
    });
    console.log('API 响应:', response.data);
    return response.data;
  } catch (error) {
    console.error('API 错误:', error.message);
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误详情:', error.response.data);
    } else if (error.request) {
      console.error('没有收到响应');
    } else {
      console.error('请求设置错误:', error.message);
    }
  }
}

// 运行测试
async function runTests() {
  console.log('开始API测试...');
  await testCheckPhone();
  console.log('--------------------------');
  await testSendSms();
  console.log('测试完成');
}

console.log('测试脚本启动');
runTests()
  .then(() => console.log('所有测试已完成'))
  .catch(err => console.error('测试过程中发生错误:', err)); 